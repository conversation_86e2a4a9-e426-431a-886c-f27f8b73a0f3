<!DOCTYPE html>
<html lang="fa" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>DNA Encryption Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background: #1a1a1a;
            color: #fff;
        }
        .test-section {
            margin: 20px 0;
            padding: 20px;
            border: 1px solid #333;
            border-radius: 8px;
            background: #2a2a2a;
        }
        .success { color: #4ade80; }
        .error { color: #ef4444; }
        .warning { color: #f59e0b; }
        button {
            background: #6366f1;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #5b5bf6;
        }
        #results {
            margin-top: 20px;
            padding: 10px;
            background: #333;
            border-radius: 5px;
            white-space: pre-wrap;
        }
    </style>
</head>
<body>
    <h1>🧬 DNA Encryption Platform - Test Page</h1>
    
    <div class="test-section">
        <h2>Module Loading Test</h2>
        <button onclick="testModuleLoading()">Test Module Loading</button>
        <div id="moduleResults"></div>
    </div>

    <div class="test-section">
        <h2>Theme & Language Test</h2>
        <button onclick="testThemeSwitch()">Test Theme Switch</button>
        <button onclick="testLanguageSwitch()">Test Language Switch</button>
        <div id="themeResults"></div>
    </div>

    <div class="test-section">
        <h2>DNA Encoding Test</h2>
        <button onclick="testDNAEncoding()">Test DNA Encoding</button>
        <button onclick="testErrorCorrection()">Test Error Correction</button>
        <div id="dnaResults"></div>
    </div>

    <div class="test-section">
        <h2>UI Components Test</h2>
        <button onclick="testNotifications()">Test Notifications</button>
        <button onclick="testProgressModal()">Test Progress Modal</button>
        <button onclick="testTabSwitching()">Test Tab Switching</button>
        <div id="uiResults"></div>
    </div>

    <div class="test-section">
        <h2>Advanced Features Test</h2>
        <button onclick="testAdvancedFeatures()">Test Advanced Features</button>
        <button onclick="test3DDNA()">Test 3D DNA Animation</button>
        <button onclick="testGSAPAnimations()">Test GSAP Animations</button>
        <button onclick="testSoundEffects()">Test Sound Effects</button>
        <div id="advancedResults"></div>
    </div>

    <div id="results"></div>

    <!-- Load the same scripts as main app -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/pako/2.1.0/pako.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/qrcode/1.5.3/qrcode.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/gsap@3.12.5/dist/gsap.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/three@0.160.0/build/three.min.js"></script>
    
    <!-- Load core modules -->
    <script src="src/core/DNACodec.js"></script>
    <script src="src/core/AdvancedCryptoEngine.js"></script>
    <script src="src/core/AdvancedFileHandler.js"></script>
    <script src="src/storage/StorageManager.js"></script>
    <script src="src/ui/UIManager.js"></script>
    <script src="src/ui/AdvancedFeatures.js"></script>
    <script src="src/DNAEncryptionApp.js"></script>

    <script>
        let testResults = [];

        function log(message, type = 'info') {
            const timestamp = new Date().toLocaleTimeString();
            testResults.push(`[${timestamp}] ${type.toUpperCase()}: ${message}`);
            updateResults();
        }

        function updateResults() {
            document.getElementById('results').textContent = testResults.join('\n');
        }

        function testModuleLoading() {
            const moduleResults = document.getElementById('moduleResults');
            const requiredClasses = ['DNACodec', 'AdvancedCryptoEngine', 'AdvancedFileHandler', 'StorageManager', 'UIManager', 'DNAEncryptionApp'];
            let results = [];

            requiredClasses.forEach(className => {
                if (typeof window[className] !== 'undefined') {
                    results.push(`<span class="success">✓ ${className} loaded</span>`);
                    log(`${className} loaded successfully`, 'success');
                } else {
                    results.push(`<span class="error">✗ ${className} missing</span>`);
                    log(`${className} failed to load`, 'error');
                }
            });

            moduleResults.innerHTML = results.join('<br>');
        }

        function testThemeSwitch() {
            try {
                const uiManager = new UIManager();
                uiManager.setTheme('light');
                log('Theme switched to light', 'success');
                
                setTimeout(() => {
                    uiManager.setTheme('dark');
                    log('Theme switched back to dark', 'success');
                }, 1000);
                
                document.getElementById('themeResults').innerHTML = '<span class="success">✓ Theme switching works</span>';
            } catch (error) {
                log(`Theme switch error: ${error.message}`, 'error');
                document.getElementById('themeResults').innerHTML = '<span class="error">✗ Theme switching failed</span>';
            }
        }

        function testLanguageSwitch() {
            try {
                const uiManager = new UIManager();
                uiManager.setLanguage('en');
                log('Language switched to English', 'success');
                
                setTimeout(() => {
                    uiManager.setLanguage('fa');
                    log('Language switched back to Persian', 'success');
                }, 1000);
                
                document.getElementById('themeResults').innerHTML += '<br><span class="success">✓ Language switching works</span>';
            } catch (error) {
                log(`Language switch error: ${error.message}`, 'error');
                document.getElementById('themeResults').innerHTML += '<br><span class="error">✗ Language switching failed</span>';
            }
        }

        function testDNAEncoding() {
            try {
                const codec = new DNACodec();
                const testData = new Uint8Array([72, 101, 108, 108, 111]); // "Hello"
                const encoded = codec.encodeWithErrorCorrection(testData);
                log(`DNA encoding successful: ${encoded.substring(0, 50)}...`, 'success');
                
                const decoded = codec.decodeWithErrorCorrection(encoded);
                const decodedText = String.fromCharCode(...decoded);
                log(`DNA decoding successful: ${decodedText}`, 'success');
                
                document.getElementById('dnaResults').innerHTML = '<span class="success">✓ DNA encoding/decoding works</span>';
            } catch (error) {
                log(`DNA encoding error: ${error.message}`, 'error');
                document.getElementById('dnaResults').innerHTML = '<span class="error">✗ DNA encoding failed</span>';
            }
        }

        function testErrorCorrection() {
            try {
                const codec = new DNACodec();
                
                // Enable error correction
                codec.setErrorCorrectionEnabled(true);
                
                // Test data
                const testData = new Uint8Array([72, 101, 108, 108, 111]); // "Hello"
                
                // Encode with error correction
                const encoded = codec.encodeWithErrorCorrection(testData);
                log(`DNA encoding with error correction: ${encoded.substring(0, 50)}...`, 'info');
                
                // Introduce some errors (replace some nucleotides)
                const corruptedDNA = introduceErrors(encoded, 5);
                log(`Corrupted DNA: ${corruptedDNA.substring(0, 50)}...`, 'warning');
                
                // Try to decode the corrupted DNA
                try {
                    const decoded = codec.decodeWithErrorCorrection(corruptedDNA);
                    const decodedText = String.fromCharCode(...decoded);
                    
                    if (decodedText === "Hello") {
                        log(`Error correction successful! Decoded: ${decodedText}`, 'success');
                        document.getElementById('dnaResults').innerHTML += '<br><span class="success">✓ Error correction works</span>';
                    } else {
                        log(`Error correction partial: ${decodedText}`, 'warning');
                        document.getElementById('dnaResults').innerHTML += '<br><span class="warning">⚠ Error correction partial</span>';
                    }
                } catch (error) {
                    log(`Error correction failed: ${error.message}`, 'error');
                    document.getElementById('dnaResults').innerHTML += '<br><span class="error">✗ Error correction failed</span>';
                }
                
            } catch (error) {
                log(`Error correction test error: ${error.message}`, 'error');
                document.getElementById('dnaResults').innerHTML += '<br><span class="error">✗ Error correction test failed</span>';
            }
        }
        
        // Helper function to introduce errors in DNA sequence
        function introduceErrors(dnaSequence, errorCount) {
            const nucleotides = ['A', 'T', 'C', 'G'];
            const dnaArray = dnaSequence.split('');
            
            for (let i = 0; i < errorCount; i++) {
                const randomPos = Math.floor(Math.random() * dnaArray.length);
                const currentNucleotide = dnaArray[randomPos];
                
                // Choose a different nucleotide
                let newNucleotide;
                do {
                    newNucleotide = nucleotides[Math.floor(Math.random() * nucleotides.length)];
                } while (newNucleotide === currentNucleotide);
                
                dnaArray[randomPos] = newNucleotide;
            }
            
            return dnaArray.join('');
        }

        function testNotifications() {
            try {
                const uiManager = new UIManager();
                uiManager.showNotification('Test notification', 'success', 'Test');
                log('Notification test successful', 'success');
                document.getElementById('uiResults').innerHTML = '<span class="success">✓ Notifications work</span>';
            } catch (error) {
                log(`Notification error: ${error.message}`, 'error');
                document.getElementById('uiResults').innerHTML = '<span class="error">✗ Notifications failed</span>';
            }
        }

        function testProgressModal() {
            try {
                const uiManager = new UIManager();

                // Test progress modal creation and display
                uiManager.showProgress('Testing progress modal...', 25);
                log('Progress modal created and shown', 'success');

                // Update progress
                setTimeout(() => {
                    uiManager.updateProgress(50, 'Half way there...');
                    log('Progress modal updated to 50%', 'success');
                }, 1000);

                // Complete and hide
                setTimeout(() => {
                    uiManager.updateProgress(100, 'Complete!');
                    setTimeout(() => {
                        uiManager.hideProgress();
                        log('Progress modal hidden', 'success');
                    }, 500);
                }, 2000);

                document.getElementById('uiResults').innerHTML += '<br><span class="success">✓ Progress modal works</span>';
            } catch (error) {
                log(`Progress modal error: ${error.message}`, 'error');
                document.getElementById('uiResults').innerHTML += '<br><span class="error">✗ Progress modal failed</span>';
            }
        }

        function testTabSwitching() {
            try {
                const uiManager = new UIManager();

                // Create mock DOM elements for testing
                const mockNav = document.createElement('div');
                mockNav.innerHTML = `
                    <div class="nav-item" data-tab="dashboard">Dashboard</div>
                    <div class="nav-item" data-tab="encrypt">Encrypt</div>
                    <div class="nav-item" data-tab="decrypt">Decrypt</div>
                `;

                const mockContent = document.createElement('div');
                mockContent.innerHTML = `
                    <div id="dashboard-tab" class="tab-content active">Dashboard Content</div>
                    <div id="encrypt-tab" class="tab-content">Encrypt Content</div>
                    <div id="decrypt-tab" class="tab-content">Decrypt Content</div>
                `;

                document.body.appendChild(mockNav);
                document.body.appendChild(mockContent);

                // Test tab switching
                uiManager.switchTab('encrypt');
                log('Switched to encrypt tab', 'success');

                setTimeout(() => {
                    uiManager.switchTab('decrypt');
                    log('Switched to decrypt tab', 'success');

                    setTimeout(() => {
                        uiManager.switchTab('dashboard');
                        log('Switched back to dashboard tab', 'success');

                        // Clean up
                        mockNav.remove();
                        mockContent.remove();
                    }, 500);
                }, 500);

                document.getElementById('uiResults').innerHTML += '<br><span class="success">✓ Tab switching works</span>';
            } catch (error) {
                log(`Tab switching error: ${error.message}`, 'error');
                document.getElementById('uiResults').innerHTML += '<br><span class="error">✗ Tab switching failed</span>';
            }
        }

        // Advanced Features tests
        function testAdvancedFeatures() {
            try {
                const advancedFeatures = new AdvancedFeatures();
                advancedFeatures.loadPreferences();
                
                const results = [];
                results.push(`<span class="success">✓ AdvancedFeatures class loaded</span>`);
                results.push(`<span class="success">✓ Preferences loaded</span>`);
                
                // Check preference values
                results.push(`<span class="info">Theme: ${advancedFeatures.preferences.theme}</span>`);
                results.push(`<span class="info">Language: ${advancedFeatures.preferences.language}</span>`);
                results.push(`<span class="info">Animations: ${advancedFeatures.preferences.animations}</span>`);
                results.push(`<span class="info">Sounds: ${advancedFeatures.preferences.sounds}</span>`);
                results.push(`<span class="info">Compression: ${advancedFeatures.preferences.compression}</span>`);
                
                document.getElementById('advancedResults').innerHTML = results.join('<br>');
                log('Advanced Features test completed successfully', 'success');
            } catch (error) {
                log(`Advanced Features test error: ${error.message}`, 'error');
                document.getElementById('advancedResults').innerHTML = '<span class="error">✗ Advanced Features test failed</span>';
            }
        }
        
        function test3DDNA() {
            try {
                // Create test container
                const testContainer = document.createElement('div');
                testContainer.id = 'dnaAnimationTest';
                testContainer.style.width = '400px';
                testContainer.style.height = '300px';
                testContainer.style.marginTop = '10px';
                testContainer.style.border = '1px solid #333';
                testContainer.style.borderRadius = '8px';
                document.getElementById('advancedResults').appendChild(testContainer);
                
                // Create advanced features
                const advancedFeatures = new AdvancedFeatures();
                advancedFeatures.animationContainer = testContainer;
                
                // Initialize 3D DNA
                if (window.THREE) {
                    advancedFeatures.init3DDNA();
                    advancedFeatures.start3DDNA();
                    log('3D DNA Animation started', 'success');
                    
                    // Add stop button
                    const stopBtn = document.createElement('button');
                    stopBtn.textContent = 'Stop Animation';
                    stopBtn.style.marginTop = '10px';
                    stopBtn.onclick = () => {
                        advancedFeatures.stop3DDNA();
                        log('3D DNA Animation stopped', 'info');
                    };
                    document.getElementById('advancedResults').appendChild(stopBtn);
                } else {
                    throw new Error('Three.js not available');
                }
            } catch (error) {
                log(`3D DNA Animation test error: ${error.message}`, 'error');
                document.getElementById('advancedResults').innerHTML = '<span class="error">✗ 3D DNA Animation test failed</span>';
            }
        }
        
        function testGSAPAnimations() {
            try {
                // Check if GSAP is available
                if (!window.gsap) {
                    throw new Error('GSAP not available');
                }
                
                // Create test elements
                const animContainer = document.createElement('div');
                animContainer.style.marginTop = '10px';
                animContainer.innerHTML = `
                    <div class="anim-test-item" style="width: 100px; height: 100px; background: #6366f1; border-radius: 8px; margin: 10px;"></div>
                    <div class="anim-test-item" style="width: 100px; height: 100px; background: #8b5cf6; border-radius: 8px; margin: 10px;"></div>
                    <div class="anim-test-item" style="width: 100px; height: 100px; background: #ec4899; border-radius: 8px; margin: 10px;"></div>
                `;
                document.getElementById('advancedResults').innerHTML = '';
                document.getElementById('advancedResults').appendChild(animContainer);
                
                // Apply animations
                const items = document.querySelectorAll('.anim-test-item');
                gsap.fromTo(items, 
                    { x: -100, opacity: 0 }, 
                    { x: 0, opacity: 1, duration: 1, stagger: 0.2, ease: 'back.out(1.7)' }
                );
                
                log('GSAP Animations test successful', 'success');
                
                // Add rotating animation
                setTimeout(() => {
                    gsap.to(items, {
                        rotation: 360,
                        duration: 2,
                        stagger: 0.3,
                        ease: 'power2.inOut'
                    });
                }, 1500);
            } catch (error) {
                log(`GSAP Animations test error: ${error.message}`, 'error');
                document.getElementById('advancedResults').innerHTML = '<span class="error">✗ GSAP Animations test failed</span>';
            }
        }
        
        function testSoundEffects() {
            try {
                // Create advanced features
                const advancedFeatures = new AdvancedFeatures();
                advancedFeatures.initializeSounds();
                
                // Create sound buttons
                const soundContainer = document.createElement('div');
                soundContainer.style.marginTop = '10px';
                soundContainer.style.display = 'flex';
                soundContainer.style.gap = '10px';
                
                const sounds = ['success', 'error', 'notification', 'click', 'complete'];
                
                sounds.forEach(sound => {
                    const btn = document.createElement('button');
                    btn.textContent = `Play ${sound}`;
                    btn.onclick = () => {
                        advancedFeatures.playSound(sound);
                        log(`Playing ${sound} sound`, 'info');
                    };
                    soundContainer.appendChild(btn);
                });
                
                document.getElementById('advancedResults').innerHTML = '<span class="success">✓ Sound system initialized</span><br>';
                document.getElementById('advancedResults').appendChild(soundContainer);
                
                log('Sound effects test ready', 'success');
            } catch (error) {
                log(`Sound effects test error: ${error.message}`, 'error');
                document.getElementById('advancedResults').innerHTML = '<span class="error">✗ Sound effects test failed</span>';
            }
        }

        // Auto-run basic tests
        window.addEventListener('load', () => {
            log('Test page loaded', 'info');
            setTimeout(() => {
                testModuleLoading();
            }, 500);
        });
    </script>
</body>
</html>
