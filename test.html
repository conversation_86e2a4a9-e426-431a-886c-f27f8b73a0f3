<!DOCTYPE html>
<html lang="fa" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🧬 DNA Encryption Platform - Comprehensive Test Suite | پلتفرم رمزگذاری DNA - مجموعه تست جامع</title>
    <style>
        :root {
            --primary-color: #6366f1;
            --success-color: #4ade80;
            --error-color: #ef4444;
            --warning-color: #f59e0b;
            --info-color: #3b82f6;
            --bg-primary: #1a1a1a;
            --bg-secondary: #2a2a2a;
            --bg-tertiary: #333;
            --text-primary: #fff;
            --text-secondary: #ccc;
            --border-color: #444;
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: var(--bg-primary);
            color: var(--text-primary);
            line-height: 1.6;
            overflow-x: hidden;
        }

        .header {
            background: linear-gradient(135deg, #6366f1, #8b5cf6);
            padding: 2rem;
            text-align: center;
            box-shadow: 0 4px 20px rgba(0,0,0,0.3);
            margin-bottom: 2rem;
        }

        .header h1 {
            font-size: 2.5rem;
            margin-bottom: 0.5rem;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.5);
        }

        .header p {
            font-size: 1.2rem;
            opacity: 0.9;
        }

        .test-controls {
            display: flex;
            justify-content: center;
            gap: 1rem;
            margin-bottom: 2rem;
            flex-wrap: wrap;
        }

        .control-btn {
            background: var(--primary-color);
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 8px;
            cursor: pointer;
            font-size: 1rem;
            font-weight: 600;
            transition: all 0.3s ease;
            box-shadow: 0 2px 10px rgba(99, 102, 241, 0.3);
        }

        .control-btn:hover {
            background: #5b5bf6;
            transform: translateY(-2px);
            box-shadow: 0 4px 20px rgba(99, 102, 241, 0.4);
        }

        .control-btn.danger {
            background: var(--error-color);
            box-shadow: 0 2px 10px rgba(239, 68, 68, 0.3);
        }

        .control-btn.danger:hover {
            background: #dc2626;
            box-shadow: 0 4px 20px rgba(239, 68, 68, 0.4);
        }

        .test-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
            gap: 2rem;
            margin-bottom: 2rem;
        }

        .test-section {
            background: var(--bg-secondary);
            border: 1px solid var(--border-color);
            border-radius: 12px;
            padding: 1.5rem;
            box-shadow: 0 4px 15px rgba(0,0,0,0.2);
            transition: all 0.3s ease;
        }

        .test-section:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(0,0,0,0.3);
        }

        .test-section h2 {
            color: var(--primary-color);
            margin-bottom: 1rem;
            font-size: 1.4rem;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .test-section .icon {
            font-size: 1.2rem;
        }

        .test-buttons {
            display: flex;
            flex-wrap: wrap;
            gap: 0.5rem;
            margin-bottom: 1rem;
        }

        .test-btn {
            background: var(--bg-tertiary);
            color: var(--text-primary);
            border: 1px solid var(--border-color);
            padding: 8px 16px;
            border-radius: 6px;
            cursor: pointer;
            font-size: 0.9rem;
            transition: all 0.3s ease;
        }

        .test-btn:hover {
            background: var(--primary-color);
            border-color: var(--primary-color);
        }

        .test-results {
            background: var(--bg-tertiary);
            border-radius: 8px;
            padding: 1rem;
            min-height: 100px;
            max-height: 300px;
            overflow-y: auto;
            font-family: 'Courier New', monospace;
            font-size: 0.9rem;
            line-height: 1.4;
        }

        .success { color: var(--success-color); }
        .error { color: var(--error-color); }
        .warning { color: var(--warning-color); }
        .info { color: var(--info-color); }

        .progress-bar {
            width: 100%;
            height: 8px;
            background: var(--bg-tertiary);
            border-radius: 4px;
            overflow: hidden;
            margin: 1rem 0;
        }

        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, var(--primary-color), var(--success-color));
            width: 0%;
            transition: width 0.3s ease;
        }

        .stats-container {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 1rem;
            margin-bottom: 2rem;
        }

        .stat-card {
            background: var(--bg-secondary);
            border: 1px solid var(--border-color);
            border-radius: 8px;
            padding: 1rem;
            text-align: center;
        }

        .stat-number {
            font-size: 2rem;
            font-weight: bold;
            color: var(--primary-color);
        }

        .stat-label {
            color: var(--text-secondary);
            font-size: 0.9rem;
        }

        #globalResults {
            background: var(--bg-secondary);
            border: 1px solid var(--border-color);
            border-radius: 12px;
            padding: 1.5rem;
            margin-top: 2rem;
            max-height: 500px;
            overflow-y: auto;
        }

        #globalResults h3 {
            color: var(--primary-color);
            margin-bottom: 1rem;
        }

        .log-entry {
            padding: 0.5rem;
            margin: 0.25rem 0;
            border-radius: 4px;
            border-left: 4px solid transparent;
        }

        .log-entry.success { border-left-color: var(--success-color); background: rgba(74, 222, 128, 0.1); }
        .log-entry.error { border-left-color: var(--error-color); background: rgba(239, 68, 68, 0.1); }
        .log-entry.warning { border-left-color: var(--warning-color); background: rgba(245, 158, 11, 0.1); }
        .log-entry.info { border-left-color: var(--info-color); background: rgba(59, 130, 246, 0.1); }

        @media (max-width: 768px) {
            .test-grid {
                grid-template-columns: 1fr;
            }

            .header h1 {
                font-size: 2rem;
            }

            .test-controls {
                flex-direction: column;
                align-items: center;
            }
        }

        .loading {
            display: inline-block;
            width: 20px;
            height: 20px;
            border: 3px solid rgba(255,255,255,.3);
            border-radius: 50%;
            border-top-color: var(--primary-color);
            animation: spin 1s ease-in-out infinite;
        }

        @keyframes spin {
            to { transform: rotate(360deg); }
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>🧬 DNA Encryption Platform - Comprehensive Test Suite</h1>
        <p>پلتفرم رمزگذاری DNA - مجموعه تست جامع | 100 تست عملکردی و امنیتی</p>
    </div>

    <div class="test-controls">
        <button class="control-btn" onclick="runAllTests()">🚀 Run All Tests | اجرای همه تست‌ها</button>
        <button class="control-btn" onclick="runCoreTests()">⚡ Core Tests | تست‌های اصلی</button>
        <button class="control-btn" onclick="runSecurityTests()">🔒 Security Tests | تست‌های امنیتی</button>
        <button class="control-btn" onclick="runPerformanceTests()">📊 Performance Tests | تست‌های عملکرد</button>
        <button class="control-btn danger" onclick="clearAllResults()">🗑️ Clear Results | پاک کردن نتایج</button>
    </div>

    <div class="stats-container">
        <div class="stat-card">
            <div class="stat-number" id="totalTests">0</div>
            <div class="stat-label">Total Tests | کل تست‌ها</div>
        </div>
        <div class="stat-card">
            <div class="stat-number" id="passedTests">0</div>
            <div class="stat-label">Passed | موفق</div>
        </div>
        <div class="stat-card">
            <div class="stat-number" id="failedTests">0</div>
            <div class="stat-label">Failed | ناموفق</div>
        </div>
        <div class="stat-card">
            <div class="stat-number" id="testProgress">0%</div>
            <div class="stat-label">Progress | پیشرفت</div>
        </div>
    </div>

    <div class="progress-bar">
        <div class="progress-fill" id="progressFill"></div>
    </div>

    <div class="test-grid">
        <!-- Module Loading Tests -->
        <div class="test-section">
            <h2><span class="icon">📦</span>Module Loading Tests | تست‌های بارگذاری ماژول</h2>
            <div class="test-buttons">
                <button class="test-btn" onclick="testModuleLoading()">Basic Loading</button>
                <button class="test-btn" onclick="testModuleDependencies()">Dependencies</button>
                <button class="test-btn" onclick="testModuleExports()">Exports</button>
                <button class="test-btn" onclick="testModuleInitialization()">Initialization</button>
                <button class="test-btn" onclick="testModuleVersions()">Versions</button>
            </div>
            <div class="test-results" id="moduleResults"></div>
        </div>

        <!-- DNA Codec Tests -->
        <div class="test-section">
            <h2><span class="icon">🧬</span>DNA Codec Tests | تست‌های کدک DNA</h2>
            <div class="test-buttons">
                <button class="test-btn" onclick="testBasicDNAEncoding()">Basic Encoding</button>
                <button class="test-btn" onclick="testDNADecoding()">Basic Decoding</button>
                <button class="test-btn" onclick="testErrorCorrection()">Error Correction</button>
                <button class="test-btn" onclick="testDNACompression()">Compression</button>
                <button class="test-btn" onclick="testDNAChecksum()">Checksum</button>
                <button class="test-btn" onclick="testDNAMetadata()">Metadata</button>
                <button class="test-btn" onclick="testDNAValidation()">Validation</button>
                <button class="test-btn" onclick="testLargeDNASequences()">Large Sequences</button>
                <button class="test-btn" onclick="testCorruptedDNA()">Corrupted DNA</button>
                <button class="test-btn" onclick="testDNAPerformance()">Performance</button>
            </div>
            <div class="test-results" id="dnaResults"></div>
        </div>

        <!-- Crypto Engine Tests -->
        <div class="test-section">
            <h2><span class="icon">🔐</span>Crypto Engine Tests | تست‌های موتور رمزگذاری</h2>
            <div class="test-buttons">
                <button class="test-btn" onclick="testAESEncryption()">AES Encryption</button>
                <button class="test-btn" onclick="testAESDecryption()">AES Decryption</button>
                <button class="test-btn" onclick="testPBKDF2KeyDerivation()">PBKDF2</button>
                <button class="test-btn" onclick="testSaltGeneration()">Salt Generation</button>
                <button class="test-btn" onclick="testIVGeneration()">IV Generation</button>
                <button class="test-btn" onclick="testPasswordStrength()">Password Strength</button>
                <button class="test-btn" onclick="testSecureRandomness()">Secure Random</button>
                <button class="test-btn" onclick="testHashFunctions()">Hash Functions</button>
                <button class="test-btn" onclick="testCryptoPerformance()">Crypto Performance</button>
                <button class="test-btn" onclick="testWrongPassword()">Wrong Password</button>
            </div>
            <div class="test-results" id="cryptoResults"></div>
        </div>

        <!-- File Handler Tests -->
        <div class="test-section">
            <h2><span class="icon">📁</span>File Handler Tests | تست‌های مدیریت فایل</h2>
            <div class="test-buttons">
                <button class="test-btn" onclick="testFileValidation()">File Validation</button>
                <button class="test-btn" onclick="testFileMetadata()">File Metadata</button>
                <button class="test-btn" onclick="testFileTypeDetection()">Type Detection</button>
                <button class="test-btn" onclick="testLargeFileHandling()">Large Files</button>
                <button class="test-btn" onclick="testFileChunking()">File Chunking</button>
                <button class="test-btn" onclick="testDNAFileExtensions()">DNA Extensions</button>
                <button class="test-btn" onclick="testFileDownload()">File Download</button>
                <button class="test-btn" onclick="testFileSizeLimit()">Size Limits</button>
                <button class="test-btn" onclick="testBinaryFiles()">Binary Files</button>
                <button class="test-btn" onclick="testTextFiles()">Text Files</button>
            </div>
            <div class="test-results" id="fileResults"></div>
        </div>

        <!-- Storage Manager Tests -->
        <div class="test-section">
            <h2><span class="icon">💾</span>Storage Tests | تست‌های ذخیره‌سازی</h2>
            <div class="test-buttons">
                <button class="test-btn" onclick="testStorageInitialization()">Initialization</button>
                <button class="test-btn" onclick="testDataStorage()">Data Storage</button>
                <button class="test-btn" onclick="testDataRetrieval()">Data Retrieval</button>
                <button class="test-btn" onclick="testDataDeletion()">Data Deletion</button>
                <button class="test-btn" onclick="testStorageQuota()">Storage Quota</button>
                <button class="test-btn" onclick="testIndexedDBOperations()">IndexedDB Ops</button>
                <button class="test-btn" onclick="testStorageEncryption()">Storage Encryption</button>
                <button class="test-btn" onclick="testStorageBackup()">Storage Backup</button>
                <button class="test-btn" onclick="testStorageCleanup()">Storage Cleanup</button>
                <button class="test-btn" onclick="testStoragePerformance()">Storage Performance</button>
            </div>
            <div class="test-results" id="storageResults"></div>
        </div>

        <!-- UI Manager Tests -->
        <div class="test-section">
            <h2><span class="icon">🎨</span>UI Manager Tests | تست‌های مدیریت رابط کاربری</h2>
            <div class="test-buttons">
                <button class="test-btn" onclick="testUIInitialization()">UI Initialization</button>
                <button class="test-btn" onclick="testThemeSwitch()">Theme Switch</button>
                <button class="test-btn" onclick="testLanguageSwitch()">Language Switch</button>
                <button class="test-btn" onclick="testNotifications()">Notifications</button>
                <button class="test-btn" onclick="testProgressModal()">Progress Modal</button>
                <button class="test-btn" onclick="testTabSwitching()">Tab Switching</button>
                <button class="test-btn" onclick="testKeyboardShortcuts()">Keyboard Shortcuts</button>
                <button class="test-btn" onclick="testResponsiveDesign()">Responsive Design</button>
                <button class="test-btn" onclick="testAccessibility()">Accessibility</button>
                <button class="test-btn" onclick="testUIAnimations()">UI Animations</button>
            </div>
            <div class="test-results" id="uiResults"></div>
        </div>

        <!-- Advanced Features Tests -->
        <div class="test-section">
            <h2><span class="icon">⚡</span>Advanced Features Tests | تست‌های ویژگی‌های پیشرفته</h2>
            <div class="test-buttons">
                <button class="test-btn" onclick="testAdvancedFeatures()">Basic Features</button>
                <button class="test-btn" onclick="test3DDNA()">3D DNA Animation</button>
                <button class="test-btn" onclick="testGSAPAnimations()">GSAP Animations</button>
                <button class="test-btn" onclick="testSoundEffects()">Sound Effects</button>
                <button class="test-btn" onclick="testPreferences()">User Preferences</button>
                <button class="test-btn" onclick="testAdvancedSettings()">Advanced Settings</button>
                <button class="test-btn" onclick="testPerformanceMode()">Performance Mode</button>
                <button class="test-btn" onclick="testFullscreenMode()">Fullscreen Mode</button>
                <button class="test-btn" onclick="testExportImport()">Export/Import</button>
                <button class="test-btn" onclick="testAdvancedAnalytics()">Advanced Analytics</button>
            </div>
            <div class="test-results" id="advancedResults"></div>
        </div>

        <!-- Security Tests -->
        <div class="test-section">
            <h2><span class="icon">🛡️</span>Security Tests | تست‌های امنیتی</h2>
            <div class="test-buttons">
                <button class="test-btn" onclick="testPasswordSecurity()">Password Security</button>
                <button class="test-btn" onclick="testDataIntegrity()">Data Integrity</button>
                <button class="test-btn" onclick="testEncryptionStrength()">Encryption Strength</button>
                <button class="test-btn" onclick="testBruteForceProtection()">Brute Force Protection</button>
                <button class="test-btn" onclick="testMemoryLeaks()">Memory Leaks</button>
                <button class="test-btn" onclick="testSecureStorage()">Secure Storage</button>
                <button class="test-btn" onclick="testInputSanitization()">Input Sanitization</button>
                <button class="test-btn" onclick="testXSSProtection()">XSS Protection</button>
                <button class="test-btn" onclick="testCSRFProtection()">CSRF Protection</button>
                <button class="test-btn" onclick="testSecureHeaders()">Secure Headers</button>
            </div>
            <div class="test-results" id="securityResults"></div>
        </div>

        <!-- Performance Tests -->
        <div class="test-section">
            <h2><span class="icon">📊</span>Performance Tests | تست‌های عملکرد</h2>
            <div class="test-buttons">
                <button class="test-btn" onclick="testEncryptionSpeed()">Encryption Speed</button>
                <button class="test-btn" onclick="testDecryptionSpeed()">Decryption Speed</button>
                <button class="test-btn" onclick="testMemoryUsage()">Memory Usage</button>
                <button class="test-btn" onclick="testCPUUsage()">CPU Usage</button>
                <button class="test-btn" onclick="testLargeFilePerformance()">Large File Performance</button>
                <button class="test-btn" onclick="testConcurrentOperations()">Concurrent Operations</button>
                <button class="test-btn" onclick="testBrowserCompatibility()">Browser Compatibility</button>
                <button class="test-btn" onclick="testMobilePerformance()">Mobile Performance</button>
                <button class="test-btn" onclick="testLoadTime()">Load Time</button>
                <button class="test-btn" onclick="testStressTest()">Stress Test</button>
            </div>
            <div class="test-results" id="performanceResults"></div>
        </div>

        <!-- Integration Tests -->
        <div class="test-section">
            <h2><span class="icon">🔗</span>Integration Tests | تست‌های یکپارچگی</h2>
            <div class="test-buttons">
                <button class="test-btn" onclick="testFullEncryptionFlow()">Full Encryption Flow</button>
                <button class="test-btn" onclick="testFullDecryptionFlow()">Full Decryption Flow</button>
                <button class="test-btn" onclick="testEndToEndEncryption()">End-to-End</button>
                <button class="test-btn" onclick="testMultipleFiles()">Multiple Files</button>
                <button class="test-btn" onclick="testDifferentFileTypes()">Different File Types</button>
                <button class="test-btn" onclick="testErrorRecovery()">Error Recovery</button>
                <button class="test-btn" onclick="testUserWorkflow()">User Workflow</button>
                <button class="test-btn" onclick="testDataPersistence()">Data Persistence</button>
                <button class="test-btn" onclick="testCrossTabCommunication()">Cross-Tab Communication</button>
                <button class="test-btn" onclick="testOfflineMode()">Offline Mode</button>
            </div>
            <div class="test-results" id="integrationResults"></div>
        </div>
    </div>

    <div id="globalResults">
        <h3>📋 Global Test Results | نتایج کلی تست‌ها</h3>
        <div id="globalResultsContent"></div>
    </div>

    <!-- Load the same scripts as main app -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/pako/2.1.0/pako.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/qrcode/1.5.3/qrcode.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/gsap@3.12.5/dist/gsap.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/three@0.160.0/build/three.min.js"></script>
    
    <!-- Load core modules -->
    <script src="src/core/DNACodec.js"></script>
    <script src="src/core/AdvancedCryptoEngine.js"></script>
    <script src="src/core/AdvancedFileHandler.js"></script>
    <script src="src/storage/StorageManager.js"></script>
    <script src="src/ui/UIManager.js"></script>
    <script src="src/ui/AdvancedFeatures.js"></script>
    <script src="src/DNAEncryptionApp.js"></script>

    <script>
        // Global test state
        let testResults = [];
        let testStats = {
            total: 0,
            passed: 0,
            failed: 0,
            running: false
        };

        // Test categories
        const testCategories = {
            module: 'Module Loading Tests',
            dna: 'DNA Codec Tests',
            crypto: 'Crypto Engine Tests',
            file: 'File Handler Tests',
            storage: 'Storage Tests',
            ui: 'UI Manager Tests',
            advanced: 'Advanced Features Tests',
            security: 'Security Tests',
            performance: 'Performance Tests',
            integration: 'Integration Tests'
        };

        // Utility functions
        function log(message, type = 'info', category = 'general') {
            const timestamp = new Date().toLocaleTimeString('fa-IR');
            const logEntry = {
                timestamp,
                message,
                type,
                category,
                id: Date.now() + Math.random()
            };

            testResults.push(logEntry);
            updateGlobalResults();

            // Update stats
            if (type === 'success') testStats.passed++;
            else if (type === 'error') testStats.failed++;

            updateStats();
        }

        function updateGlobalResults() {
            const container = document.getElementById('globalResultsContent');
            const latest = testResults.slice(-50); // Show last 50 entries

            container.innerHTML = latest.map(entry => `
                <div class="log-entry ${entry.type}">
                    <strong>[${entry.timestamp}]</strong>
                    <span class="category">[${entry.category}]</span>
                    ${entry.message}
                </div>
            `).join('');

            container.scrollTop = container.scrollHeight;
        }

        function updateStats() {
            document.getElementById('totalTests').textContent = testStats.total;
            document.getElementById('passedTests').textContent = testStats.passed;
            document.getElementById('failedTests').textContent = testStats.failed;

            const progress = testStats.total > 0 ? Math.round((testStats.passed + testStats.failed) / testStats.total * 100) : 0;
            document.getElementById('testProgress').textContent = progress + '%';
            document.getElementById('progressFill').style.width = progress + '%';
        }

        function updateCategoryResults(categoryId, message, type = 'info') {
            const container = document.getElementById(categoryId + 'Results');
            if (container) {
                const entry = document.createElement('div');
                entry.className = `log-entry ${type}`;
                entry.innerHTML = `<strong>[${new Date().toLocaleTimeString('fa-IR')}]</strong> ${message}`;
                container.appendChild(entry);
                container.scrollTop = container.scrollHeight;
            }
        }

        function clearAllResults() {
            testResults = [];
            testStats = { total: 0, passed: 0, failed: 0, running: false };

            // Clear all category results
            Object.keys(testCategories).forEach(category => {
                const container = document.getElementById(category + 'Results');
                if (container) container.innerHTML = '';
            });

            document.getElementById('globalResultsContent').innerHTML = '';
            updateStats();
            log('همه نتایج پاک شد | All results cleared', 'info', 'system');
        }

        // Test execution helpers
        async function runTest(testName, testFunction, category = 'general') {
            testStats.total++;
            updateStats();

            try {
                log(`شروع تست: ${testName} | Starting test: ${testName}`, 'info', category);
                updateCategoryResults(category, `🔄 Running: ${testName}`, 'info');

                await testFunction();

                log(`✅ تست موفق: ${testName} | Test passed: ${testName}`, 'success', category);
                updateCategoryResults(category, `✅ Passed: ${testName}`, 'success');

                return true;
            } catch (error) {
                log(`❌ تست ناموفق: ${testName} - ${error.message} | Test failed: ${testName} - ${error.message}`, 'error', category);
                updateCategoryResults(category, `❌ Failed: ${testName} - ${error.message}`, 'error');

                return false;
            }
        }

        function sleep(ms) {
            return new Promise(resolve => setTimeout(resolve, ms));
        }

        function generateRandomData(size) {
            const data = new Uint8Array(size);
            crypto.getRandomValues(data);
            return data;
        }

        function generateRandomString(length) {
            const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
            let result = '';
            for (let i = 0; i < length; i++) {
                result += chars.charAt(Math.floor(Math.random() * chars.length));
            }
            return result;
        }

        function measurePerformance(fn) {
            const start = performance.now();
            const result = fn();
            const end = performance.now();
            return { result, time: end - start };
        }

        async function measureAsyncPerformance(fn) {
            const start = performance.now();
            const result = await fn();
            const end = performance.now();
            return { result, time: end - start };
        }

        // ==================== MODULE LOADING TESTS ====================

        async function testModuleLoading() {
            await runTest('Basic Module Loading', async () => {
                const requiredClasses = ['DNACodec', 'AdvancedCryptoEngine', 'AdvancedFileHandler', 'StorageManager', 'UIManager', 'DNAEncryptionApp'];
                const missing = requiredClasses.filter(className => typeof window[className] === 'undefined');

                if (missing.length > 0) {
                    throw new Error(`Missing classes: ${missing.join(', ')}`);
                }

                log(`All ${requiredClasses.length} core modules loaded successfully`, 'success', 'module');
            }, 'module');
        }

        async function testModuleDependencies() {
            await runTest('Module Dependencies', async () => {
                // Test if modules can be instantiated
                const dnaCodec = new DNACodec();
                const cryptoEngine = new AdvancedCryptoEngine();
                const fileHandler = new AdvancedFileHandler();
                const storageManager = new StorageManager();
                const uiManager = new UIManager();

                if (!dnaCodec || !cryptoEngine || !fileHandler || !storageManager || !uiManager) {
                    throw new Error('Failed to instantiate core modules');
                }

                log('All modules instantiated successfully', 'success', 'module');
            }, 'module');
        }

        async function testModuleExports() {
            await runTest('Module Exports', async () => {
                // Test if modules have required methods
                const dnaCodec = new DNACodec();
                const requiredMethods = ['encodeWithErrorCorrection', 'decodeWithErrorCorrection', 'cleanDNASequence'];

                requiredMethods.forEach(method => {
                    if (typeof dnaCodec[method] !== 'function') {
                        throw new Error(`DNACodec missing method: ${method}`);
                    }
                });

                log('All required methods are exported', 'success', 'module');
            }, 'module');
        }

        async function testModuleInitialization() {
            await runTest('Module Initialization', async () => {
                const app = new DNAEncryptionApp();

                if (!app.dnaCodec || !app.cryptoEngine || !app.fileHandler) {
                    throw new Error('App modules not properly initialized');
                }

                log('Application modules initialized correctly', 'success', 'module');
            }, 'module');
        }

        async function testModuleVersions() {
            await runTest('Module Versions', async () => {
                const app = new DNAEncryptionApp();

                if (!app.version || !app.developer) {
                    throw new Error('Version information missing');
                }

                if (app.version !== '2.1.0' || app.developer !== 'Mezd') {
                    throw new Error('Incorrect version information');
                }

                log(`Version: ${app.version}, Developer: ${app.developer}`, 'success', 'module');
            }, 'module');
        }

        // ==================== DNA CODEC TESTS ====================

        async function testBasicDNAEncoding() {
            await runTest('Basic DNA Encoding', async () => {
                const codec = new DNACodec();
                const testData = new Uint8Array([72, 101, 108, 108, 111]); // "Hello"
                const encoded = codec.encodeWithErrorCorrection(testData);

                if (!encoded || typeof encoded !== 'string') {
                    throw new Error('Encoding failed or returned invalid data');
                }

                if (!/^[ATCG]+$/.test(encoded)) {
                    throw new Error('Encoded data contains invalid nucleotides');
                }

                log(`Encoded ${testData.length} bytes to ${encoded.length} nucleotides`, 'success', 'dna');
            }, 'dna');
        }

        async function testDNADecoding() {
            await runTest('Basic DNA Decoding', async () => {
                const codec = new DNACodec();
                const testData = new Uint8Array([72, 101, 108, 108, 111]); // "Hello"
                const encoded = codec.encodeWithErrorCorrection(testData);
                const decoded = codec.decodeWithErrorCorrection(encoded);

                if (!decoded || decoded.length !== testData.length) {
                    throw new Error('Decoding failed or returned wrong length');
                }

                for (let i = 0; i < testData.length; i++) {
                    if (decoded[i] !== testData[i]) {
                        throw new Error(`Decoded data mismatch at position ${i}`);
                    }
                }

                log('DNA decoding successful, data integrity verified', 'success', 'dna');
            }, 'dna');
        }

        async function testErrorCorrection() {
            await runTest('Error Correction', async () => {
                const codec = new DNACodec();
                codec.setErrorCorrectionEnabled(true);

                const testData = new Uint8Array([72, 101, 108, 108, 111]); // "Hello"
                const encoded = codec.encodeWithErrorCorrection(testData);

                // Introduce some errors
                const corruptedDNA = introduceErrors(encoded, 3);

                try {
                    const decoded = codec.decodeWithErrorCorrection(corruptedDNA);
                    const decodedText = String.fromCharCode(...decoded);

                    if (decodedText === "Hello") {
                        log('Error correction successful', 'success', 'dna');
                    } else {
                        log('Partial error correction', 'warning', 'dna');
                    }
                } catch (error) {
                    throw new Error(`Error correction failed: ${error.message}`);
                }
            }, 'dna');
        }

        async function testDNACompression() {
            await runTest('DNA Compression', async () => {
                const codec = new DNACodec();
                const largeData = generateRandomData(1000);

                const { result: encoded, time } = await measureAsyncPerformance(async () => {
                    return codec.encodeWithErrorCorrection(largeData);
                });

                const compressionRatio = encoded.length / (largeData.length * 8); // 8 bits per byte

                if (compressionRatio > 2) {
                    throw new Error(`Poor compression ratio: ${compressionRatio.toFixed(2)}`);
                }

                log(`Compression ratio: ${compressionRatio.toFixed(2)}, Time: ${time.toFixed(2)}ms`, 'success', 'dna');
            }, 'dna');
        }

        async function testDNAChecksum() {
            await runTest('DNA Checksum', async () => {
                const codec = new DNACodec();
                const testData = generateRandomData(100);
                const encoded = codec.encodeWithErrorCorrection(testData);

                // Corrupt one nucleotide
                const corrupted = encoded.substring(0, 10) + 'X' + encoded.substring(11);

                try {
                    codec.decodeWithErrorCorrection(corrupted);
                    throw new Error('Checksum validation should have failed');
                } catch (error) {
                    if (error.message.includes('checksum') || error.message.includes('corrupted')) {
                        log('Checksum validation working correctly', 'success', 'dna');
                    } else {
                        throw error;
                    }
                }
            }, 'dna');
        }

        async function testDNAMetadata() {
            await runTest('DNA Metadata', async () => {
                const codec = new DNACodec();
                const testData = generateRandomData(50);
                const encoded = codec.encodeWithErrorCorrection(testData);
                const decoded = codec.decodeWithErrorCorrection(encoded);

                // Test if metadata is preserved
                const originalExtension = codec.getOriginalFileExtension();

                if (decoded.length !== testData.length) {
                    throw new Error('Metadata corruption affected data integrity');
                }

                log('Metadata handling verified', 'success', 'dna');
            }, 'dna');
        }

        async function testDNAValidation() {
            await runTest('DNA Validation', async () => {
                const codec = new DNACodec();

                // Test valid DNA
                const validDNA = 'ATCGATCGATCG';
                const cleaned = codec.cleanDNASequence(validDNA);

                if (cleaned !== validDNA) {
                    throw new Error('Valid DNA sequence was modified');
                }

                // Test invalid DNA
                const invalidDNA = 'ATCGXYZ123';
                const cleanedInvalid = codec.cleanDNASequence(invalidDNA);

                if (cleanedInvalid !== 'ATCG') {
                    throw new Error('Invalid characters not properly removed');
                }

                log('DNA validation working correctly', 'success', 'dna');
            }, 'dna');
        }

        async function testLargeDNASequences() {
            await runTest('Large DNA Sequences', async () => {
                const codec = new DNACodec();
                const largeData = generateRandomData(10000); // 10KB

                const { result: encoded, time: encodeTime } = await measureAsyncPerformance(async () => {
                    return codec.encodeWithErrorCorrection(largeData);
                });

                const { result: decoded, time: decodeTime } = await measureAsyncPerformance(async () => {
                    return codec.decodeWithErrorCorrection(encoded);
                });

                if (decoded.length !== largeData.length) {
                    throw new Error('Large sequence processing failed');
                }

                log(`Large sequence: ${largeData.length} bytes, Encode: ${encodeTime.toFixed(2)}ms, Decode: ${decodeTime.toFixed(2)}ms`, 'success', 'dna');
            }, 'dna');
        }

        async function testCorruptedDNA() {
            await runTest('Corrupted DNA Handling', async () => {
                const codec = new DNACodec();
                const testData = generateRandomData(100);
                const encoded = codec.encodeWithErrorCorrection(testData);

                // Heavily corrupt the DNA (50% corruption)
                const heavilyCorrupted = introduceErrors(encoded, Math.floor(encoded.length * 0.5));

                try {
                    const decoded = codec.decodeWithErrorCorrection(heavilyCorrupted);
                    log('Heavily corrupted DNA handled gracefully', 'warning', 'dna');
                } catch (error) {
                    if (error.message.includes('corrupted') || error.message.includes('checksum')) {
                        log('Corrupted DNA properly rejected', 'success', 'dna');
                    } else {
                        throw error;
                    }
                }
            }, 'dna');
        }

        async function testDNAPerformance() {
            await runTest('DNA Performance', async () => {
                const codec = new DNACodec();
                const testSizes = [100, 1000, 5000];
                const results = [];

                for (const size of testSizes) {
                    const data = generateRandomData(size);

                    const { time: encodeTime } = await measureAsyncPerformance(async () => {
                        return codec.encodeWithErrorCorrection(data);
                    });

                    results.push(`${size}B: ${encodeTime.toFixed(2)}ms`);
                }

                log(`Performance results: ${results.join(', ')}`, 'success', 'dna');
            }, 'dna');
        }

        // ==================== CRYPTO ENGINE TESTS ====================

        async function testAESEncryption() {
            await runTest('AES Encryption', async () => {
                const crypto = new AdvancedCryptoEngine();
                const testData = generateRandomData(1000);
                const password = 'TestPassword123!';

                const encrypted = await crypto.encryptBeforeDNA(testData, password);

                if (!encrypted || encrypted.length === 0) {
                    throw new Error('Encryption failed');
                }

                if (encrypted.length <= testData.length) {
                    throw new Error('Encrypted data should be larger due to salt and IV');
                }

                log(`Encrypted ${testData.length} bytes to ${encrypted.length} bytes`, 'success', 'crypto');
            }, 'crypto');
        }

        async function testAESDecryption() {
            await runTest('AES Decryption', async () => {
                const crypto = new AdvancedCryptoEngine();
                const testData = generateRandomData(1000);
                const password = 'TestPassword123!';

                const encrypted = await crypto.encryptBeforeDNA(testData, password);
                const decrypted = await crypto.decryptAfterDNA(encrypted, password);

                if (decrypted.length !== testData.length) {
                    throw new Error('Decrypted data length mismatch');
                }

                for (let i = 0; i < testData.length; i++) {
                    if (decrypted[i] !== testData[i]) {
                        throw new Error(`Data mismatch at position ${i}`);
                    }
                }

                log('AES decryption successful, data integrity verified', 'success', 'crypto');
            }, 'crypto');
        }

        async function testPBKDF2KeyDerivation() {
            await runTest('PBKDF2 Key Derivation', async () => {
                const crypto = new AdvancedCryptoEngine();
                const password = 'TestPassword123!';
                const salt = new Uint8Array(32);
                window.crypto.getRandomValues(salt);

                const key1 = await crypto.deriveKey(password, salt);
                const key2 = await crypto.deriveKey(password, salt);

                // Keys should be the same for same password and salt
                if (!key1 || !key2) {
                    throw new Error('Key derivation failed');
                }

                // Test with different salt
                const differentSalt = new Uint8Array(32);
                window.crypto.getRandomValues(differentSalt);
                const key3 = await crypto.deriveKey(password, differentSalt);

                if (!key3) {
                    throw new Error('Key derivation with different salt failed');
                }

                log('PBKDF2 key derivation working correctly', 'success', 'crypto');
            }, 'crypto');
        }

        async function testSaltGeneration() {
            await runTest('Salt Generation', async () => {
                const crypto = new AdvancedCryptoEngine();
                const salts = [];

                // Generate multiple salts
                for (let i = 0; i < 10; i++) {
                    const testData = generateRandomData(100);
                    const encrypted = await crypto.encryptBeforeDNA(testData, 'password');
                    const salt = encrypted.slice(0, crypto.saltLength);
                    salts.push(Array.from(salt).join(','));
                }

                // Check for uniqueness
                const uniqueSalts = new Set(salts);
                if (uniqueSalts.size !== salts.length) {
                    throw new Error('Salt generation not producing unique values');
                }

                log(`Generated ${salts.length} unique salts`, 'success', 'crypto');
            }, 'crypto');
        }

        async function testIVGeneration() {
            await runTest('IV Generation', async () => {
                const crypto = new AdvancedCryptoEngine();
                const ivs = [];

                // Generate multiple IVs
                for (let i = 0; i < 10; i++) {
                    const testData = generateRandomData(100);
                    const encrypted = await crypto.encryptBeforeDNA(testData, 'password');
                    const iv = encrypted.slice(crypto.saltLength, crypto.saltLength + crypto.ivLength);
                    ivs.push(Array.from(iv).join(','));
                }

                // Check for uniqueness
                const uniqueIVs = new Set(ivs);
                if (uniqueIVs.size !== ivs.length) {
                    throw new Error('IV generation not producing unique values');
                }

                log(`Generated ${ivs.length} unique IVs`, 'success', 'crypto');
            }, 'crypto');
        }

        async function testPasswordStrength() {
            await runTest('Password Strength', async () => {
                const crypto = new AdvancedCryptoEngine();

                // Test secure password generation
                const password = crypto.generateSecurePassword(32);

                if (password.length !== 32) {
                    throw new Error('Generated password has wrong length');
                }

                // Check for character variety
                const hasUpper = /[A-Z]/.test(password);
                const hasLower = /[a-z]/.test(password);
                const hasNumber = /[0-9]/.test(password);
                const hasSpecial = /[!@#$%^&*]/.test(password);

                if (!hasUpper || !hasLower || !hasNumber || !hasSpecial) {
                    throw new Error('Generated password lacks character variety');
                }

                log(`Generated secure password with all character types`, 'success', 'crypto');
            }, 'crypto');
        }

        async function testSecureRandomness() {
            await runTest('Secure Randomness', async () => {
                const crypto = new AdvancedCryptoEngine();
                const passwords = [];

                // Generate multiple passwords
                for (let i = 0; i < 100; i++) {
                    passwords.push(crypto.generateSecurePassword(16));
                }

                // Check for uniqueness
                const uniquePasswords = new Set(passwords);
                if (uniquePasswords.size !== passwords.length) {
                    throw new Error('Password generation not producing unique values');
                }

                log(`Generated ${passwords.length} unique secure passwords`, 'success', 'crypto');
            }, 'crypto');
        }

        async function testHashFunctions() {
            await runTest('Hash Functions', async () => {
                const crypto = new AdvancedCryptoEngine();
                const testData = generateRandomData(1000);

                const hash1 = await crypto.hashData(testData);
                const hash2 = await crypto.hashData(testData);

                if (!hash1 || !hash2) {
                    throw new Error('Hash generation failed');
                }

                if (hash1.length !== hash2.length) {
                    throw new Error('Hash lengths inconsistent');
                }

                // Hashes should be identical for same data
                for (let i = 0; i < hash1.length; i++) {
                    if (hash1[i] !== hash2[i]) {
                        throw new Error('Hash values inconsistent for same data');
                    }
                }

                log(`Generated consistent ${hash1.length}-byte hashes`, 'success', 'crypto');
            }, 'crypto');
        }

        async function testCryptoPerformance() {
            await runTest('Crypto Performance', async () => {
                const crypto = new AdvancedCryptoEngine();
                const testSizes = [1000, 10000, 50000];
                const results = [];

                for (const size of testSizes) {
                    const data = generateRandomData(size);
                    const password = 'TestPassword123!';

                    const { time: encryptTime } = await measureAsyncPerformance(async () => {
                        return await crypto.encryptBeforeDNA(data, password);
                    });

                    results.push(`${size}B: ${encryptTime.toFixed(2)}ms`);
                }

                log(`Crypto performance: ${results.join(', ')}`, 'success', 'crypto');
            }, 'crypto');
        }

        async function testWrongPassword() {
            await runTest('Wrong Password Handling', async () => {
                const crypto = new AdvancedCryptoEngine();
                const testData = generateRandomData(1000);
                const correctPassword = 'CorrectPassword123!';
                const wrongPassword = 'WrongPassword456!';

                const encrypted = await crypto.encryptBeforeDNA(testData, correctPassword);

                try {
                    await crypto.decryptAfterDNA(encrypted, wrongPassword);
                    throw new Error('Decryption should have failed with wrong password');
                } catch (error) {
                    if (error.message.includes('Decryption failed') || error.message.includes('Invalid password')) {
                        log('Wrong password properly rejected', 'success', 'crypto');
                    } else {
                        throw error;
                    }
                }
            }, 'crypto');
        }

        // ==================== FILE HANDLER TESTS ====================

        async function testFileValidation() {
            await runTest('File Validation', async () => {
                const fileHandler = new AdvancedFileHandler();

                // Create mock files
                const validFile = new File(['Hello World'], 'test.txt', { type: 'text/plain' });
                const emptyFile = new File([''], 'empty.txt', { type: 'text/plain' });
                const largeFile = new File([new ArrayBuffer(100 * 1024 * 1024)], 'large.bin', { type: 'application/octet-stream' });

                const validResult = fileHandler.validateFile(validFile);
                const emptyResult = fileHandler.validateFile(emptyFile);
                const largeResult = fileHandler.validateFile(largeFile);

                if (!validResult.isValid) {
                    throw new Error('Valid file rejected');
                }

                if (emptyResult.isValid) {
                    throw new Error('Empty file should be rejected');
                }

                if (largeResult.isValid) {
                    throw new Error('Oversized file should be rejected');
                }

                log('File validation working correctly', 'success', 'file');
            }, 'file');
        }

        async function testFileMetadata() {
            await runTest('File Metadata', async () => {
                const fileHandler = new AdvancedFileHandler();
                const testFile = new File(['Test content'], 'test.txt', { type: 'text/plain' });

                const metadata = fileHandler.extractFileMetadata(testFile);

                if (!metadata.name || !metadata.size || !metadata.type || !metadata.lastModified) {
                    throw new Error('Incomplete metadata extraction');
                }

                if (metadata.name !== 'test.txt' || metadata.type !== 'text/plain') {
                    throw new Error('Incorrect metadata values');
                }

                log(`Metadata: ${metadata.name}, ${metadata.size} bytes, ${metadata.type}`, 'success', 'file');
            }, 'file');
        }

        async function testFileTypeDetection() {
            await runTest('File Type Detection', async () => {
                const fileHandler = new AdvancedFileHandler();

                const textFile = new File(['Hello'], 'test.txt', { type: 'text/plain' });
                const imageFile = new File([new ArrayBuffer(1000)], 'test.jpg', { type: 'image/jpeg' });
                const binaryFile = new File([new ArrayBuffer(1000)], 'test.bin', { type: 'application/octet-stream' });

                const textType = fileHandler.detectFileType(textFile);
                const imageType = fileHandler.detectFileType(imageFile);
                const binaryType = fileHandler.detectFileType(binaryFile);

                if (textType !== 'text' || imageType !== 'image' || binaryType !== 'binary') {
                    throw new Error('File type detection failed');
                }

                log('File type detection working correctly', 'success', 'file');
            }, 'file');
        }

        async function testLargeFileHandling() {
            await runTest('Large File Handling', async () => {
                const fileHandler = new AdvancedFileHandler();
                const largeData = generateRandomData(1024 * 1024); // 1MB
                const largeFile = new File([largeData], 'large.bin', { type: 'application/octet-stream' });

                const { result: processedData, time } = await measureAsyncPerformance(async () => {
                    return await fileHandler.processLargeFile(largeFile);
                });

                if (!processedData || processedData.length !== largeData.length) {
                    throw new Error('Large file processing failed');
                }

                log(`Processed ${largeData.length} bytes in ${time.toFixed(2)}ms`, 'success', 'file');
            }, 'file');
        }

        async function testFileChunking() {
            await runTest('File Chunking', async () => {
                const fileHandler = new AdvancedFileHandler();
                const testData = generateRandomData(100000); // 100KB
                const testFile = new File([testData], 'test.bin', { type: 'application/octet-stream' });

                const chunks = await fileHandler.chunkFile(testFile, 32768); // 32KB chunks

                if (!chunks || chunks.length === 0) {
                    throw new Error('File chunking failed');
                }

                const expectedChunks = Math.ceil(testData.length / 32768);
                if (chunks.length !== expectedChunks) {
                    throw new Error(`Expected ${expectedChunks} chunks, got ${chunks.length}`);
                }

                log(`File chunked into ${chunks.length} pieces`, 'success', 'file');
            }, 'file');
        }

        async function testDNAFileExtensions() {
            await runTest('DNA File Extensions', async () => {
                const fileHandler = new AdvancedFileHandler();

                const dnaFilename = fileHandler.generateDNAFilename('document.pdf');

                if (!dnaFilename.endsWith('.dna')) {
                    throw new Error('DNA filename should have .dna extension');
                }

                if (!dnaFilename.includes('document')) {
                    throw new Error('DNA filename should preserve original name');
                }

                const isDNAFile = fileHandler.isDNAEncryptedFile(dnaFilename);
                if (!isDNAFile) {
                    throw new Error('DNA file not properly detected');
                }

                log(`Generated DNA filename: ${dnaFilename}`, 'success', 'file');
            }, 'file');
        }

        async function testFileDownload() {
            await runTest('File Download', async () => {
                const fileHandler = new AdvancedFileHandler();
                const testData = new Uint8Array([72, 101, 108, 108, 111]); // "Hello"
                const filename = 'test.txt';

                // Mock download function
                const originalCreateElement = document.createElement;
                let downloadTriggered = false;

                document.createElement = function(tagName) {
                    if (tagName === 'a') {
                        const mockLink = {
                            href: '',
                            download: '',
                            click: () => { downloadTriggered = true; },
                            style: { display: '' }
                        };
                        return mockLink;
                    }
                    return originalCreateElement.call(document, tagName);
                };

                fileHandler.downloadFile(testData, filename);

                // Restore original function
                document.createElement = originalCreateElement;

                if (!downloadTriggered) {
                    throw new Error('File download not triggered');
                }

                log('File download mechanism working', 'success', 'file');
            }, 'file');
        }

        async function testFileSizeLimit() {
            await runTest('File Size Limit', async () => {
                const fileHandler = new AdvancedFileHandler();
                const maxSize = fileHandler.getMaxFileSize();

                if (!maxSize || maxSize <= 0) {
                    throw new Error('Invalid max file size');
                }

                const oversizedFile = new File([new ArrayBuffer(maxSize + 1)], 'huge.bin', { type: 'application/octet-stream' });
                const validation = fileHandler.validateFile(oversizedFile);

                if (validation.isValid) {
                    throw new Error('Oversized file should be rejected');
                }

                log(`Max file size: ${(maxSize / 1024 / 1024).toFixed(2)}MB`, 'success', 'file');
            }, 'file');
        }

        async function testBinaryFiles() {
            await runTest('Binary File Processing', async () => {
                const fileHandler = new AdvancedFileHandler();
                const binaryData = generateRandomData(1000);
                const binaryFile = new File([binaryData], 'test.bin', { type: 'application/octet-stream' });

                const processed = await fileHandler.processLargeFile(binaryFile);

                if (!processed || processed.length !== binaryData.length) {
                    throw new Error('Binary file processing failed');
                }

                for (let i = 0; i < binaryData.length; i++) {
                    if (processed[i] !== binaryData[i]) {
                        throw new Error(`Binary data corruption at position ${i}`);
                    }
                }

                log('Binary file processing successful', 'success', 'file');
            }, 'file');
        }

        async function testTextFiles() {
            await runTest('Text File Processing', async () => {
                const fileHandler = new AdvancedFileHandler();
                const textContent = 'Hello World! این یک متن فارسی است. 🧬';
                const textFile = new File([textContent], 'test.txt', { type: 'text/plain' });

                const processed = await fileHandler.processLargeFile(textFile);
                const processedText = new TextDecoder('utf-8').decode(processed);

                if (processedText !== textContent) {
                    throw new Error('Text file processing corrupted content');
                }

                log('Text file processing with UTF-8 successful', 'success', 'file');
            }, 'file');
        }

        function testDNAEncoding() {
            try {
                const codec = new DNACodec();
                const testData = new Uint8Array([72, 101, 108, 108, 111]); // "Hello"
                const encoded = codec.encodeWithErrorCorrection(testData);
                log(`DNA encoding successful: ${encoded.substring(0, 50)}...`, 'success');
                
                const decoded = codec.decodeWithErrorCorrection(encoded);
                const decodedText = String.fromCharCode(...decoded);
                log(`DNA decoding successful: ${decodedText}`, 'success');
                
                document.getElementById('dnaResults').innerHTML = '<span class="success">✓ DNA encoding/decoding works</span>';
            } catch (error) {
                log(`DNA encoding error: ${error.message}`, 'error');
                document.getElementById('dnaResults').innerHTML = '<span class="error">✗ DNA encoding failed</span>';
            }
        }

        function testErrorCorrection() {
            try {
                const codec = new DNACodec();
                
                // Enable error correction
                codec.setErrorCorrectionEnabled(true);
                
                // Test data
                const testData = new Uint8Array([72, 101, 108, 108, 111]); // "Hello"
                
                // Encode with error correction
                const encoded = codec.encodeWithErrorCorrection(testData);
                log(`DNA encoding with error correction: ${encoded.substring(0, 50)}...`, 'info');
                
                // Introduce some errors (replace some nucleotides)
                const corruptedDNA = introduceErrors(encoded, 5);
                log(`Corrupted DNA: ${corruptedDNA.substring(0, 50)}...`, 'warning');
                
                // Try to decode the corrupted DNA
                try {
                    const decoded = codec.decodeWithErrorCorrection(corruptedDNA);
                    const decodedText = String.fromCharCode(...decoded);
                    
                    if (decodedText === "Hello") {
                        log(`Error correction successful! Decoded: ${decodedText}`, 'success');
                        document.getElementById('dnaResults').innerHTML += '<br><span class="success">✓ Error correction works</span>';
                    } else {
                        log(`Error correction partial: ${decodedText}`, 'warning');
                        document.getElementById('dnaResults').innerHTML += '<br><span class="warning">⚠ Error correction partial</span>';
                    }
                } catch (error) {
                    log(`Error correction failed: ${error.message}`, 'error');
                    document.getElementById('dnaResults').innerHTML += '<br><span class="error">✗ Error correction failed</span>';
                }
                
            } catch (error) {
                log(`Error correction test error: ${error.message}`, 'error');
                document.getElementById('dnaResults').innerHTML += '<br><span class="error">✗ Error correction test failed</span>';
            }
        }

        // ==================== STORAGE MANAGER TESTS ====================

        async function testStorageInitialization() {
            await runTest('Storage Initialization', async () => {
                const storage = new StorageManager();
                await storage.initialize();

                if (!storage.db) {
                    throw new Error('Storage database not initialized');
                }

                log('Storage manager initialized successfully', 'success', 'storage');
            }, 'storage');
        }

        async function testDataStorage() {
            await runTest('Data Storage', async () => {
                const storage = new StorageManager();
                await storage.initialize();

                const testData = {
                    id: 'test-' + Date.now(),
                    filename: 'test.txt',
                    size: 1000,
                    timestamp: new Date(),
                    metadata: { type: 'text/plain' }
                };

                await storage.storeEncryption(testData);

                const retrieved = await storage.getEncryption(testData.id);

                if (!retrieved || retrieved.filename !== testData.filename) {
                    throw new Error('Data storage/retrieval failed');
                }

                log(`Stored and retrieved data for ${testData.filename}`, 'success', 'storage');
            }, 'storage');
        }

        async function testDataRetrieval() {
            await runTest('Data Retrieval', async () => {
                const storage = new StorageManager();
                await storage.initialize();

                const allData = await storage.getAllEncryptions();

                if (!Array.isArray(allData)) {
                    throw new Error('Data retrieval returned invalid format');
                }

                log(`Retrieved ${allData.length} stored items`, 'success', 'storage');
            }, 'storage');
        }

        async function testDataDeletion() {
            await runTest('Data Deletion', async () => {
                const storage = new StorageManager();
                await storage.initialize();

                const testData = {
                    id: 'delete-test-' + Date.now(),
                    filename: 'delete-test.txt',
                    size: 500,
                    timestamp: new Date()
                };

                await storage.storeEncryption(testData);
                await storage.deleteEncryption(testData.id);

                const retrieved = await storage.getEncryption(testData.id);

                if (retrieved) {
                    throw new Error('Data deletion failed');
                }

                log('Data deletion successful', 'success', 'storage');
            }, 'storage');
        }

        async function testStorageQuota() {
            await runTest('Storage Quota', async () => {
                const storage = new StorageManager();
                await storage.initialize();

                const quota = await storage.getStorageQuota();

                if (!quota || !quota.usage || !quota.quota) {
                    throw new Error('Storage quota information unavailable');
                }

                const usagePercent = (quota.usage / quota.quota * 100).toFixed(2);

                log(`Storage usage: ${usagePercent}% (${quota.usage}/${quota.quota} bytes)`, 'success', 'storage');
            }, 'storage');
        }

        async function testIndexedDBOperations() {
            await runTest('IndexedDB Operations', async () => {
                const storage = new StorageManager();
                await storage.initialize();

                // Test transaction handling
                const transaction = storage.db.transaction(['encryptions'], 'readwrite');
                const objectStore = transaction.objectStore('encryptions');

                if (!objectStore) {
                    throw new Error('IndexedDB object store not accessible');
                }

                log('IndexedDB operations working correctly', 'success', 'storage');
            }, 'storage');
        }

        async function testStorageEncryption() {
            await runTest('Storage Encryption', async () => {
                const storage = new StorageManager();
                await storage.initialize();

                const sensitiveData = {
                    id: 'encrypted-test-' + Date.now(),
                    filename: 'sensitive.txt',
                    encryptedData: generateRandomData(1000),
                    salt: generateRandomData(32),
                    iv: generateRandomData(16)
                };

                await storage.storeEncryption(sensitiveData);
                const retrieved = await storage.getEncryption(sensitiveData.id);

                if (!retrieved || !retrieved.encryptedData) {
                    throw new Error('Encrypted data storage failed');
                }

                log('Encrypted data stored and retrieved successfully', 'success', 'storage');
            }, 'storage');
        }

        async function testStorageBackup() {
            await runTest('Storage Backup', async () => {
                const storage = new StorageManager();
                await storage.initialize();

                const backup = await storage.exportData();

                if (!backup || !Array.isArray(backup)) {
                    throw new Error('Storage backup failed');
                }

                log(`Backup created with ${backup.length} items`, 'success', 'storage');
            }, 'storage');
        }

        async function testStorageCleanup() {
            await runTest('Storage Cleanup', async () => {
                const storage = new StorageManager();
                await storage.initialize();

                const beforeCount = (await storage.getAllEncryptions()).length;
                await storage.cleanupOldEntries(30); // Clean entries older than 30 days
                const afterCount = (await storage.getAllEncryptions()).length;

                log(`Cleanup completed: ${beforeCount} -> ${afterCount} items`, 'success', 'storage');
            }, 'storage');
        }

        async function testStoragePerformance() {
            await runTest('Storage Performance', async () => {
                const storage = new StorageManager();
                await storage.initialize();

                const testItems = [];
                for (let i = 0; i < 10; i++) {
                    testItems.push({
                        id: 'perf-test-' + i + '-' + Date.now(),
                        filename: `test-${i}.txt`,
                        size: Math.random() * 10000,
                        timestamp: new Date()
                    });
                }

                const { time: storeTime } = await measureAsyncPerformance(async () => {
                    for (const item of testItems) {
                        await storage.storeEncryption(item);
                    }
                });

                const { time: retrieveTime } = await measureAsyncPerformance(async () => {
                    for (const item of testItems) {
                        await storage.getEncryption(item.id);
                    }
                });

                log(`Storage performance: Store ${storeTime.toFixed(2)}ms, Retrieve ${retrieveTime.toFixed(2)}ms`, 'success', 'storage');
            }, 'storage');
        }

        // Helper function to introduce errors in DNA sequence
        function introduceErrors(dnaSequence, errorCount) {
            const nucleotides = ['A', 'T', 'C', 'G'];
            const dnaArray = dnaSequence.split('');
            
            for (let i = 0; i < errorCount; i++) {
                const randomPos = Math.floor(Math.random() * dnaArray.length);
                const currentNucleotide = dnaArray[randomPos];
                
                // Choose a different nucleotide
                let newNucleotide;
                do {
                    newNucleotide = nucleotides[Math.floor(Math.random() * nucleotides.length)];
                } while (newNucleotide === currentNucleotide);
                
                dnaArray[randomPos] = newNucleotide;
            }
            
            return dnaArray.join('');
        }

        // ==================== UI MANAGER TESTS ====================

        async function testUIInitialization() {
            await runTest('UI Initialization', async () => {
                const uiManager = new UIManager();
                await uiManager.initialize();

                if (!uiManager.progressModal || !uiManager.notificationContainer) {
                    throw new Error('UI components not properly initialized');
                }

                log('UI Manager initialized successfully', 'success', 'ui');
            }, 'ui');
        }

        async function testThemeSwitch() {
            await runTest('Theme Switch', async () => {
                const uiManager = new UIManager();
                await uiManager.initialize();

                const originalTheme = uiManager.currentTheme;
                const newTheme = originalTheme === 'dark' ? 'light' : 'dark';

                uiManager.setTheme(newTheme);

                if (uiManager.currentTheme !== newTheme) {
                    throw new Error('Theme switch failed');
                }

                // Switch back
                uiManager.setTheme(originalTheme);

                log(`Theme switched: ${originalTheme} -> ${newTheme} -> ${originalTheme}`, 'success', 'ui');
            }, 'ui');
        }

        async function testLanguageSwitch() {
            await runTest('Language Switch', async () => {
                const uiManager = new UIManager();
                await uiManager.initialize();

                const originalLang = uiManager.currentLanguage;
                const newLang = originalLang === 'fa' ? 'en' : 'fa';

                uiManager.setLanguage(newLang);

                if (uiManager.currentLanguage !== newLang) {
                    throw new Error('Language switch failed');
                }

                // Test translation
                const translation = uiManager.translate('processing');
                if (!translation) {
                    throw new Error('Translation system not working');
                }

                // Switch back
                uiManager.setLanguage(originalLang);

                log(`Language switched: ${originalLang} -> ${newLang} -> ${originalLang}`, 'success', 'ui');
            }, 'ui');
        }

        async function testNotifications() {
            await runTest('Notifications', async () => {
                const uiManager = new UIManager();
                await uiManager.initialize();

                // Test different notification types
                const types = ['success', 'error', 'warning', 'info'];

                for (const type of types) {
                    uiManager.showNotification(`Test ${type} notification`, type, 'Test');
                    await sleep(100);
                }

                // Test notification count
                if (uiManager.notifications.length !== types.length) {
                    throw new Error('Notification count mismatch');
                }

                log(`Created ${types.length} different notification types`, 'success', 'ui');
            }, 'ui');
        }

        async function testProgressModal() {
            await runTest('Progress Modal', async () => {
                const uiManager = new UIManager();
                await uiManager.initialize();

                // Test progress modal creation and display
                uiManager.showProgress('Testing progress modal...', 25);
                await sleep(500);

                // Update progress
                uiManager.updateProgress(50, 'Half way there...');
                await sleep(500);

                // Complete and hide
                uiManager.updateProgress(100, 'Complete!');
                await sleep(500);

                uiManager.hideProgress();

                log('Progress modal lifecycle completed successfully', 'success', 'ui');
            }, 'ui');
        }

        async function testTabSwitching() {
            await runTest('Tab Switching', async () => {
                const uiManager = new UIManager();
                await uiManager.initialize();

                const tabs = ['dashboard', 'encrypt', 'decrypt', 'analytics', 'history', 'settings'];

                for (const tab of tabs) {
                    uiManager.switchTab(tab);

                    if (uiManager.currentTab !== tab) {
                        throw new Error(`Failed to switch to ${tab} tab`);
                    }

                    await sleep(100);
                }

                log(`Successfully switched through ${tabs.length} tabs`, 'success', 'ui');
            }, 'ui');
        }

        async function testKeyboardShortcuts() {
            await runTest('Keyboard Shortcuts', async () => {
                const uiManager = new UIManager();
                await uiManager.initialize();

                // Test shortcut registration
                const shortcuts = Object.keys(uiManager.shortcuts);

                if (shortcuts.length === 0) {
                    throw new Error('No keyboard shortcuts registered');
                }

                // Test specific shortcuts
                const testShortcuts = ['ctrl+e', 'ctrl+d', 'ctrl+h'];

                for (const shortcut of testShortcuts) {
                    if (!uiManager.shortcuts[shortcut]) {
                        throw new Error(`Shortcut ${shortcut} not registered`);
                    }
                }

                log(`${shortcuts.length} keyboard shortcuts registered`, 'success', 'ui');
            }, 'ui');
        }

        async function testResponsiveDesign() {
            await runTest('Responsive Design', async () => {
                const uiManager = new UIManager();
                await uiManager.initialize();

                // Test different viewport sizes
                const originalWidth = window.innerWidth;
                const originalHeight = window.innerHeight;

                // Simulate mobile viewport
                Object.defineProperty(window, 'innerWidth', { value: 375, configurable: true });
                Object.defineProperty(window, 'innerHeight', { value: 667, configurable: true });

                window.dispatchEvent(new Event('resize'));
                await sleep(100);

                // Simulate tablet viewport
                Object.defineProperty(window, 'innerWidth', { value: 768, configurable: true });
                Object.defineProperty(window, 'innerHeight', { value: 1024, configurable: true });

                window.dispatchEvent(new Event('resize'));
                await sleep(100);

                // Restore original viewport
                Object.defineProperty(window, 'innerWidth', { value: originalWidth, configurable: true });
                Object.defineProperty(window, 'innerHeight', { value: originalHeight, configurable: true });

                window.dispatchEvent(new Event('resize'));

                log('Responsive design handling tested', 'success', 'ui');
            }, 'ui');
        }

        async function testAccessibility() {
            await runTest('Accessibility', async () => {
                const uiManager = new UIManager();
                await uiManager.initialize();

                // Test ARIA attributes
                const buttons = document.querySelectorAll('button');
                let ariaCount = 0;

                buttons.forEach(button => {
                    if (button.getAttribute('aria-label') || button.getAttribute('aria-describedby')) {
                        ariaCount++;
                    }
                });

                // Test keyboard navigation
                const focusableElements = document.querySelectorAll('button, input, select, textarea, [tabindex]:not([tabindex="-1"])');

                if (focusableElements.length === 0) {
                    throw new Error('No focusable elements found');
                }

                log(`Accessibility: ${ariaCount} ARIA attributes, ${focusableElements.length} focusable elements`, 'success', 'ui');
            }, 'ui');
        }

        async function testUIAnimations() {
            await runTest('UI Animations', async () => {
                const uiManager = new UIManager();
                await uiManager.initialize();

                // Test animation toggle
                const originalState = uiManager.animationEnabled;

                uiManager.toggleAnimations();

                if (uiManager.animationEnabled === originalState) {
                    throw new Error('Animation toggle failed');
                }

                // Toggle back
                uiManager.toggleAnimations();

                if (uiManager.animationEnabled !== originalState) {
                    throw new Error('Animation toggle restoration failed');
                }

                log('UI animation controls working correctly', 'success', 'ui');
            }, 'ui');
        }

        // ==================== ADVANCED FEATURES TESTS ====================

        async function testAdvancedFeatures() {
            await runTest('Advanced Features Initialization', async () => {
                const advancedFeatures = new AdvancedFeatures();
                await advancedFeatures.initialize();

                if (!advancedFeatures.preferences) {
                    throw new Error('Advanced features preferences not initialized');
                }

                log('Advanced features initialized successfully', 'success', 'advanced');
            }, 'advanced');
        }

        async function test3DDNA() {
            await runTest('3D DNA Animation', async () => {
                const advancedFeatures = new AdvancedFeatures();
                await advancedFeatures.initialize();

                // Test 3D DNA initialization (if Three.js is available)
                if (window.THREE) {
                    advancedFeatures.init3DDNA();

                    if (!advancedFeatures.scene || !advancedFeatures.camera || !advancedFeatures.renderer) {
                        throw new Error('3D DNA components not properly initialized');
                    }

                    log('3D DNA animation initialized successfully', 'success', 'advanced');
                } else {
                    log('Three.js not available, skipping 3D DNA test', 'warning', 'advanced');
                }
            }, 'advanced');
        }

        async function testGSAPAnimations() {
            await runTest('GSAP Animations', async () => {
                const advancedFeatures = new AdvancedFeatures();
                await advancedFeatures.initialize();

                // Test GSAP availability and basic animation
                if (window.gsap) {
                    const testElement = document.createElement('div');
                    document.body.appendChild(testElement);

                    gsap.to(testElement, { duration: 0.1, x: 100 });

                    await sleep(150);

                    const transform = testElement.style.transform;
                    if (!transform.includes('translateX')) {
                        throw new Error('GSAP animation not applied');
                    }

                    document.body.removeChild(testElement);
                    log('GSAP animations working correctly', 'success', 'advanced');
                } else {
                    log('GSAP not available, skipping animation test', 'warning', 'advanced');
                }
            }, 'advanced');
        }

        async function testSoundEffects() {
            await runTest('Sound Effects', async () => {
                const advancedFeatures = new AdvancedFeatures();
                await advancedFeatures.initialize();

                // Test sound system initialization
                if (advancedFeatures.audio) {
                    const soundTypes = ['success', 'error', 'notification', 'click', 'complete'];

                    for (const soundType of soundTypes) {
                        if (advancedFeatures.audio[soundType] === undefined) {
                            throw new Error(`Sound effect ${soundType} not initialized`);
                        }
                    }

                    log(`${soundTypes.length} sound effects initialized`, 'success', 'advanced');
                } else {
                    throw new Error('Sound system not initialized');
                }
            }, 'advanced');
        }

        async function testPreferences() {
            await runTest('User Preferences', async () => {
                const advancedFeatures = new AdvancedFeatures();
                await advancedFeatures.initialize();

                const originalTheme = advancedFeatures.preferences.theme;
                const newTheme = originalTheme === 'dark' ? 'light' : 'dark';

                // Test preference update
                advancedFeatures.updatePreference('theme', newTheme);

                if (advancedFeatures.preferences.theme !== newTheme) {
                    throw new Error('Preference update failed');
                }

                // Test preference persistence
                advancedFeatures.savePreferences();
                advancedFeatures.loadPreferences();

                if (advancedFeatures.preferences.theme !== newTheme) {
                    throw new Error('Preference persistence failed');
                }

                // Restore original
                advancedFeatures.updatePreference('theme', originalTheme);

                log('User preferences system working correctly', 'success', 'advanced');
            }, 'advanced');
        }

        async function testAdvancedSettings() {
            await runTest('Advanced Settings', async () => {
                const advancedFeatures = new AdvancedFeatures();
                await advancedFeatures.initialize();

                // Test compression level setting
                const originalCompression = advancedFeatures.preferences.compression;
                const newCompression = originalCompression === 9 ? 5 : 9;

                advancedFeatures.updatePreference('compression', newCompression);

                if (advancedFeatures.preferences.compression !== newCompression) {
                    throw new Error('Compression setting update failed');
                }

                // Test error correction setting
                const originalErrorCorrection = advancedFeatures.preferences.errorCorrection;
                advancedFeatures.updatePreference('errorCorrection', !originalErrorCorrection);

                if (advancedFeatures.preferences.errorCorrection === originalErrorCorrection) {
                    throw new Error('Error correction setting update failed');
                }

                // Restore originals
                advancedFeatures.updatePreference('compression', originalCompression);
                advancedFeatures.updatePreference('errorCorrection', originalErrorCorrection);

                log('Advanced settings management working correctly', 'success', 'advanced');
            }, 'advanced');
        }

        async function testPerformanceMode() {
            await runTest('Performance Mode', async () => {
                const advancedFeatures = new AdvancedFeatures();
                await advancedFeatures.initialize();

                // Test performance mode toggle
                const originalAnimations = advancedFeatures.preferences.animations;
                const originalSounds = advancedFeatures.preferences.sounds;

                advancedFeatures.enablePerformanceMode();

                if (advancedFeatures.preferences.animations || advancedFeatures.preferences.sounds) {
                    throw new Error('Performance mode not properly enabled');
                }

                advancedFeatures.disablePerformanceMode();

                if (!advancedFeatures.preferences.animations || !advancedFeatures.preferences.sounds) {
                    throw new Error('Performance mode not properly disabled');
                }

                log('Performance mode toggle working correctly', 'success', 'advanced');
            }, 'advanced');
        }

        async function testFullscreenMode() {
            await runTest('Fullscreen Mode', async () => {
                const advancedFeatures = new AdvancedFeatures();
                await advancedFeatures.initialize();

                // Test fullscreen API availability
                if (document.fullscreenEnabled || document.webkitFullscreenEnabled || document.mozFullScreenEnabled) {
                    // Mock fullscreen functionality for testing
                    let isFullscreen = false;

                    const mockEnterFullscreen = () => { isFullscreen = true; };
                    const mockExitFullscreen = () => { isFullscreen = false; };

                    mockEnterFullscreen();
                    if (!isFullscreen) {
                        throw new Error('Fullscreen enter simulation failed');
                    }

                    mockExitFullscreen();
                    if (isFullscreen) {
                        throw new Error('Fullscreen exit simulation failed');
                    }

                    log('Fullscreen mode functionality available', 'success', 'advanced');
                } else {
                    log('Fullscreen API not supported in this browser', 'warning', 'advanced');
                }
            }, 'advanced');
        }

        async function testExportImport() {
            await runTest('Export/Import Settings', async () => {
                const advancedFeatures = new AdvancedFeatures();
                await advancedFeatures.initialize();

                // Test settings export
                const exportedSettings = advancedFeatures.exportSettings();

                if (!exportedSettings || typeof exportedSettings !== 'object') {
                    throw new Error('Settings export failed');
                }

                // Test settings import
                const modifiedSettings = { ...exportedSettings, theme: 'light', language: 'en' };
                advancedFeatures.importSettings(modifiedSettings);

                if (advancedFeatures.preferences.theme !== 'light' || advancedFeatures.preferences.language !== 'en') {
                    throw new Error('Settings import failed');
                }

                // Restore original settings
                advancedFeatures.importSettings(exportedSettings);

                log('Export/Import settings working correctly', 'success', 'advanced');
            }, 'advanced');
        }

        async function testAdvancedAnalytics() {
            await runTest('Advanced Analytics', async () => {
                const advancedFeatures = new AdvancedFeatures();
                await advancedFeatures.initialize();

                // Test analytics data collection
                const analyticsData = advancedFeatures.getAnalyticsData();

                if (!analyticsData || typeof analyticsData !== 'object') {
                    throw new Error('Analytics data collection failed');
                }

                // Test performance metrics
                const performanceMetrics = advancedFeatures.getPerformanceMetrics();

                if (!performanceMetrics || !performanceMetrics.loadTime) {
                    throw new Error('Performance metrics collection failed');
                }

                log('Advanced analytics system working correctly', 'success', 'advanced');
            }, 'advanced');
        }

        function testTabSwitching() {
            try {
                const uiManager = new UIManager();

                // Create mock DOM elements for testing
                const mockNav = document.createElement('div');
                mockNav.innerHTML = `
                    <div class="nav-item" data-tab="dashboard">Dashboard</div>
                    <div class="nav-item" data-tab="encrypt">Encrypt</div>
                    <div class="nav-item" data-tab="decrypt">Decrypt</div>
                `;

                const mockContent = document.createElement('div');
                mockContent.innerHTML = `
                    <div id="dashboard-tab" class="tab-content active">Dashboard Content</div>
                    <div id="encrypt-tab" class="tab-content">Encrypt Content</div>
                    <div id="decrypt-tab" class="tab-content">Decrypt Content</div>
                `;

                document.body.appendChild(mockNav);
                document.body.appendChild(mockContent);

                // Test tab switching
                uiManager.switchTab('encrypt');
                log('Switched to encrypt tab', 'success');

                setTimeout(() => {
                    uiManager.switchTab('decrypt');
                    log('Switched to decrypt tab', 'success');

                    setTimeout(() => {
                        uiManager.switchTab('dashboard');
                        log('Switched back to dashboard tab', 'success');

                        // Clean up
                        mockNav.remove();
                        mockContent.remove();
                    }, 500);
                }, 500);

                document.getElementById('uiResults').innerHTML += '<br><span class="success">✓ Tab switching works</span>';
            } catch (error) {
                log(`Tab switching error: ${error.message}`, 'error');
                document.getElementById('uiResults').innerHTML += '<br><span class="error">✗ Tab switching failed</span>';
            }
        }

        // Advanced Features tests
        function testAdvancedFeatures() {
            try {
                const advancedFeatures = new AdvancedFeatures();
                advancedFeatures.loadPreferences();
                
                const results = [];
                results.push(`<span class="success">✓ AdvancedFeatures class loaded</span>`);
                results.push(`<span class="success">✓ Preferences loaded</span>`);
                
                // Check preference values
                results.push(`<span class="info">Theme: ${advancedFeatures.preferences.theme}</span>`);
                results.push(`<span class="info">Language: ${advancedFeatures.preferences.language}</span>`);
                results.push(`<span class="info">Animations: ${advancedFeatures.preferences.animations}</span>`);
                results.push(`<span class="info">Sounds: ${advancedFeatures.preferences.sounds}</span>`);
                results.push(`<span class="info">Compression: ${advancedFeatures.preferences.compression}</span>`);
                
                document.getElementById('advancedResults').innerHTML = results.join('<br>');
                log('Advanced Features test completed successfully', 'success');
            } catch (error) {
                log(`Advanced Features test error: ${error.message}`, 'error');
                document.getElementById('advancedResults').innerHTML = '<span class="error">✗ Advanced Features test failed</span>';
            }
        }
        
        function test3DDNA() {
            try {
                // Create test container
                const testContainer = document.createElement('div');
                testContainer.id = 'dnaAnimationTest';
                testContainer.style.width = '400px';
                testContainer.style.height = '300px';
                testContainer.style.marginTop = '10px';
                testContainer.style.border = '1px solid #333';
                testContainer.style.borderRadius = '8px';
                document.getElementById('advancedResults').appendChild(testContainer);
                
                // Create advanced features
                const advancedFeatures = new AdvancedFeatures();
                advancedFeatures.animationContainer = testContainer;
                
                // Initialize 3D DNA
                if (window.THREE) {
                    advancedFeatures.init3DDNA();
                    advancedFeatures.start3DDNA();
                    log('3D DNA Animation started', 'success');
                    
                    // Add stop button
                    const stopBtn = document.createElement('button');
                    stopBtn.textContent = 'Stop Animation';
                    stopBtn.style.marginTop = '10px';
                    stopBtn.onclick = () => {
                        advancedFeatures.stop3DDNA();
                        log('3D DNA Animation stopped', 'info');
                    };
                    document.getElementById('advancedResults').appendChild(stopBtn);
                } else {
                    throw new Error('Three.js not available');
                }
            } catch (error) {
                log(`3D DNA Animation test error: ${error.message}`, 'error');
                document.getElementById('advancedResults').innerHTML = '<span class="error">✗ 3D DNA Animation test failed</span>';
            }
        }
        
        function testGSAPAnimations() {
            try {
                // Check if GSAP is available
                if (!window.gsap) {
                    throw new Error('GSAP not available');
                }
                
                // Create test elements
                const animContainer = document.createElement('div');
                animContainer.style.marginTop = '10px';
                animContainer.innerHTML = `
                    <div class="anim-test-item" style="width: 100px; height: 100px; background: #6366f1; border-radius: 8px; margin: 10px;"></div>
                    <div class="anim-test-item" style="width: 100px; height: 100px; background: #8b5cf6; border-radius: 8px; margin: 10px;"></div>
                    <div class="anim-test-item" style="width: 100px; height: 100px; background: #ec4899; border-radius: 8px; margin: 10px;"></div>
                `;
                document.getElementById('advancedResults').innerHTML = '';
                document.getElementById('advancedResults').appendChild(animContainer);
                
                // Apply animations
                const items = document.querySelectorAll('.anim-test-item');
                gsap.fromTo(items, 
                    { x: -100, opacity: 0 }, 
                    { x: 0, opacity: 1, duration: 1, stagger: 0.2, ease: 'back.out(1.7)' }
                );
                
                log('GSAP Animations test successful', 'success');
                
                // Add rotating animation
                setTimeout(() => {
                    gsap.to(items, {
                        rotation: 360,
                        duration: 2,
                        stagger: 0.3,
                        ease: 'power2.inOut'
                    });
                }, 1500);
            } catch (error) {
                log(`GSAP Animations test error: ${error.message}`, 'error');
                document.getElementById('advancedResults').innerHTML = '<span class="error">✗ GSAP Animations test failed</span>';
            }
        }
        
        function testSoundEffects() {
            try {
                // Create advanced features
                const advancedFeatures = new AdvancedFeatures();
                advancedFeatures.initializeSounds();
                
                // Create sound buttons
                const soundContainer = document.createElement('div');
                soundContainer.style.marginTop = '10px';
                soundContainer.style.display = 'flex';
                soundContainer.style.gap = '10px';
                
                const sounds = ['success', 'error', 'notification', 'click', 'complete'];
                
                sounds.forEach(sound => {
                    const btn = document.createElement('button');
                    btn.textContent = `Play ${sound}`;
                    btn.onclick = () => {
                        advancedFeatures.playSound(sound);
                        log(`Playing ${sound} sound`, 'info');
                    };
                    soundContainer.appendChild(btn);
                });
                
                document.getElementById('advancedResults').innerHTML = '<span class="success">✓ Sound system initialized</span><br>';
                document.getElementById('advancedResults').appendChild(soundContainer);
                
                log('Sound effects test ready', 'success');
            } catch (error) {
                log(`Sound effects test error: ${error.message}`, 'error');
                document.getElementById('advancedResults').innerHTML = '<span class="error">✗ Sound effects test failed</span>';
            }
        }

        // ==================== SECURITY TESTS ====================

        async function testPasswordSecurity() {
            await runTest('Password Security', async () => {
                const crypto = new AdvancedCryptoEngine();

                // Test weak password detection
                const weakPasswords = ['123456', 'password', 'abc123'];
                const strongPassword = 'MyStr0ng!P@ssw0rd#2024';

                for (const weak of weakPasswords) {
                    const strength = crypto.checkPasswordStrength(weak);
                    if (strength > 2) {
                        throw new Error(`Weak password "${weak}" rated as strong`);
                    }
                }

                const strongStrength = crypto.checkPasswordStrength(strongPassword);
                if (strongStrength < 4) {
                    throw new Error('Strong password not properly rated');
                }

                log('Password security validation working correctly', 'success', 'security');
            }, 'security');
        }

        async function testDataIntegrity() {
            await runTest('Data Integrity', async () => {
                const codec = new DNACodec();
                const crypto = new AdvancedCryptoEngine();

                const originalData = generateRandomData(1000);
                const password = 'TestPassword123!';

                // Full encryption + DNA encoding cycle
                const encrypted = await crypto.encryptBeforeDNA(originalData, password);
                const dnaEncoded = codec.encodeWithErrorCorrection(encrypted);
                const dnaDecoded = codec.decodeWithErrorCorrection(dnaEncoded);
                const decrypted = await crypto.decryptAfterDNA(dnaDecoded, password);

                // Verify data integrity
                if (decrypted.length !== originalData.length) {
                    throw new Error('Data integrity check failed: length mismatch');
                }

                for (let i = 0; i < originalData.length; i++) {
                    if (decrypted[i] !== originalData[i]) {
                        throw new Error(`Data integrity check failed at position ${i}`);
                    }
                }

                log('Data integrity maintained through full cycle', 'success', 'security');
            }, 'security');
        }

        async function testEncryptionStrength() {
            await runTest('Encryption Strength', async () => {
                const crypto = new AdvancedCryptoEngine();
                const testData = generateRandomData(1000);
                const password = 'TestPassword123!';

                // Encrypt same data multiple times
                const encrypted1 = await crypto.encryptBeforeDNA(testData, password);
                const encrypted2 = await crypto.encryptBeforeDNA(testData, password);

                // Results should be different due to random salt and IV
                let differences = 0;
                for (let i = 0; i < Math.min(encrypted1.length, encrypted2.length); i++) {
                    if (encrypted1[i] !== encrypted2[i]) differences++;
                }

                if (differences < encrypted1.length * 0.5) {
                    throw new Error('Encryption not producing sufficiently random results');
                }

                log(`Encryption strength verified: ${differences}/${encrypted1.length} bytes different`, 'success', 'security');
            }, 'security');
        }

        async function testBruteForceProtection() {
            await runTest('Brute Force Protection', async () => {
                const crypto = new AdvancedCryptoEngine();
                const testData = generateRandomData(100);
                const correctPassword = 'CorrectPassword123!';

                const encrypted = await crypto.encryptBeforeDNA(testData, correctPassword);

                // Test multiple wrong passwords
                const wrongPasswords = ['wrong1', 'wrong2', 'wrong3', 'password', '123456'];
                let failedAttempts = 0;

                for (const wrongPassword of wrongPasswords) {
                    try {
                        await crypto.decryptAfterDNA(encrypted, wrongPassword);
                        throw new Error(`Wrong password "${wrongPassword}" should have failed`);
                    } catch (error) {
                        if (error.message.includes('Decryption failed') || error.message.includes('Invalid password')) {
                            failedAttempts++;
                        } else {
                            throw error;
                        }
                    }
                }

                if (failedAttempts !== wrongPasswords.length) {
                    throw new Error('Brute force protection not working correctly');
                }

                log(`Brute force protection: ${failedAttempts}/${wrongPasswords.length} wrong passwords rejected`, 'success', 'security');
            }, 'security');
        }

        async function testMemoryLeaks() {
            await runTest('Memory Leaks', async () => {
                const initialMemory = performance.memory ? performance.memory.usedJSHeapSize : 0;

                // Perform multiple encryption/decryption cycles
                for (let i = 0; i < 10; i++) {
                    const crypto = new AdvancedCryptoEngine();
                    const codec = new DNACodec();
                    const data = generateRandomData(1000);
                    const password = 'TestPassword123!';

                    const encrypted = await crypto.encryptBeforeDNA(data, password);
                    const encoded = codec.encodeWithErrorCorrection(encrypted);
                    const decoded = codec.decodeWithErrorCorrection(encoded);
                    const decrypted = await crypto.decryptAfterDNA(decoded, password);

                    // Clear references
                    data.fill(0);
                    encrypted.fill(0);
                    decrypted.fill(0);
                }

                // Force garbage collection if available
                if (window.gc) {
                    window.gc();
                }

                const finalMemory = performance.memory ? performance.memory.usedJSHeapSize : 0;
                const memoryIncrease = finalMemory - initialMemory;

                log(`Memory usage change: ${(memoryIncrease / 1024 / 1024).toFixed(2)}MB`, 'success', 'security');
            }, 'security');
        }

        async function testSecureStorage() {
            await runTest('Secure Storage', async () => {
                const storage = new StorageManager();
                await storage.initialize();

                const sensitiveData = {
                    id: 'secure-test-' + Date.now(),
                    filename: 'sensitive.txt',
                    encryptedData: generateRandomData(1000),
                    salt: generateRandomData(32),
                    iv: generateRandomData(16),
                    metadata: { sensitive: true }
                };

                await storage.storeEncryption(sensitiveData);
                const retrieved = await storage.getEncryption(sensitiveData.id);

                // Verify data is stored securely
                if (!retrieved || !retrieved.encryptedData) {
                    throw new Error('Secure storage failed');
                }

                // Clean up
                await storage.deleteEncryption(sensitiveData.id);

                log('Secure storage functionality verified', 'success', 'security');
            }, 'security');
        }

        async function testInputSanitization() {
            await runTest('Input Sanitization', async () => {
                const fileHandler = new AdvancedFileHandler();

                // Test malicious filenames
                const maliciousNames = [
                    '<script>alert("xss")</script>.txt',
                    '../../etc/passwd',
                    'file' + String.fromCharCode(0) + '.txt',
                    'CON.txt', // Windows reserved name
                    'file with' + String.fromCharCode(10) + 'newline.txt'
                ];

                for (const maliciousName of maliciousNames) {
                    const sanitized = fileHandler.sanitizeFilename(maliciousName);

                    if (sanitized.includes('<script>') || sanitized.includes('..') || sanitized.includes(String.fromCharCode(0))) {
                        throw new Error(`Filename sanitization failed for: ${maliciousName}`);
                    }
                }

                log('Input sanitization working correctly', 'success', 'security');
            }, 'security');
        }

        async function testXSSProtection() {
            await runTest('XSS Protection', async () => {
                const uiManager = new UIManager();
                await uiManager.initialize();

                // Test XSS in notifications
                const xssPayloads = [
                    '<script>alert("xss")</script>',
                    '<img src="x" onerror="alert(1)">',
                    'javascript:alert("xss")',
                    '<svg onload="alert(1)">'
                ];

                for (const payload of xssPayloads) {
                    uiManager.showNotification(payload, 'info', 'XSS Test');

                    // Check if payload was escaped
                    const notifications = document.querySelectorAll('.notification');
                    const lastNotification = notifications[notifications.length - 1];

                    if (lastNotification && lastNotification.innerHTML.includes('<script>')) {
                        throw new Error('XSS payload not properly escaped');
                    }
                }

                log('XSS protection working correctly', 'success', 'security');
            }, 'security');
        }

        async function testCSRFProtection() {
            await runTest('CSRF Protection', async () => {
                // Test CSRF token generation and validation
                const app = new DNAEncryptionApp();

                const token1 = app.generateCSRFToken();
                const token2 = app.generateCSRFToken();

                if (!token1 || !token2) {
                    throw new Error('CSRF token generation failed');
                }

                if (token1 === token2) {
                    throw new Error('CSRF tokens should be unique');
                }

                // Test token validation
                const isValid = app.validateCSRFToken(token1);
                if (!isValid) {
                    throw new Error('CSRF token validation failed');
                }

                log('CSRF protection mechanisms working correctly', 'success', 'security');
            }, 'security');
        }

        async function testSecureHeaders() {
            await runTest('Secure Headers', async () => {
                // Test Content Security Policy
                const csp = document.querySelector('meta[http-equiv="Content-Security-Policy"]');
                if (!csp) {
                    log('CSP header not found (may be set by server)', 'warning', 'security');
                } else {
                    log('CSP header found', 'success', 'security');
                }

                // Test other security headers (these would typically be set by server)
                const securityHeaders = [
                    'X-Frame-Options',
                    'X-Content-Type-Options',
                    'X-XSS-Protection',
                    'Strict-Transport-Security'
                ];

                log('Security headers check completed (server-side headers not testable from client)', 'success', 'security');
            }, 'security');
        }

        // ==================== PERFORMANCE TESTS ====================

        async function testEncryptionSpeed() {
            await runTest('Encryption Speed', async () => {
                const crypto = new AdvancedCryptoEngine();
                const testSizes = [1000, 10000, 100000]; // 1KB, 10KB, 100KB
                const results = [];

                for (const size of testSizes) {
                    const data = generateRandomData(size);
                    const password = 'TestPassword123!';

                    const { time } = await measureAsyncPerformance(async () => {
                        return await crypto.encryptBeforeDNA(data, password);
                    });

                    const throughput = (size / 1024 / (time / 1000)).toFixed(2); // KB/s
                    results.push(`${size}B: ${time.toFixed(2)}ms (${throughput} KB/s)`);
                }

                log(`Encryption speed: ${results.join(', ')}`, 'success', 'performance');
            }, 'performance');
        }

        async function testDecryptionSpeed() {
            await runTest('Decryption Speed', async () => {
                const crypto = new AdvancedCryptoEngine();
                const testSizes = [1000, 10000, 100000]; // 1KB, 10KB, 100KB
                const results = [];

                for (const size of testSizes) {
                    const data = generateRandomData(size);
                    const password = 'TestPassword123!';
                    const encrypted = await crypto.encryptBeforeDNA(data, password);

                    const { time } = await measureAsyncPerformance(async () => {
                        return await crypto.decryptAfterDNA(encrypted, password);
                    });

                    const throughput = (size / 1024 / (time / 1000)).toFixed(2); // KB/s
                    results.push(`${size}B: ${time.toFixed(2)}ms (${throughput} KB/s)`);
                }

                log(`Decryption speed: ${results.join(', ')}`, 'success', 'performance');
            }, 'performance');
        }

        async function testMemoryUsage() {
            await runTest('Memory Usage', async () => {
                if (!performance.memory) {
                    log('Memory API not available in this browser', 'warning', 'performance');
                    return;
                }

                const initialMemory = performance.memory.usedJSHeapSize;
                const crypto = new AdvancedCryptoEngine();
                const codec = new DNACodec();

                // Process large data
                const largeData = generateRandomData(1024 * 1024); // 1MB
                const password = 'TestPassword123!';

                const encrypted = await crypto.encryptBeforeDNA(largeData, password);
                const encoded = codec.encodeWithErrorCorrection(encrypted);
                const decoded = codec.decodeWithErrorCorrection(encoded);
                const decrypted = await crypto.decryptAfterDNA(decoded, password);

                const peakMemory = performance.memory.usedJSHeapSize;
                const memoryUsed = (peakMemory - initialMemory) / 1024 / 1024; // MB

                log(`Memory usage: ${memoryUsed.toFixed(2)}MB for 1MB data processing`, 'success', 'performance');
            }, 'performance');
        }

        async function testCPUUsage() {
            await runTest('CPU Usage', async () => {
                const startTime = performance.now();
                const crypto = new AdvancedCryptoEngine();

                // CPU intensive operations
                const iterations = 10;
                const dataSize = 50000; // 50KB

                for (let i = 0; i < iterations; i++) {
                    const data = generateRandomData(dataSize);
                    const password = 'TestPassword123!';

                    const encrypted = await crypto.encryptBeforeDNA(data, password);
                    await crypto.decryptAfterDNA(encrypted, password);
                }

                const totalTime = performance.now() - startTime;
                const avgTimePerIteration = totalTime / iterations;

                log(`CPU performance: ${avgTimePerIteration.toFixed(2)}ms avg per ${dataSize}B operation`, 'success', 'performance');
            }, 'performance');
        }

        async function testLargeFilePerformance() {
            await runTest('Large File Performance', async () => {
                const fileHandler = new AdvancedFileHandler();
                const crypto = new AdvancedCryptoEngine();
                const codec = new DNACodec();

                // Simulate large file (5MB)
                const largeData = generateRandomData(5 * 1024 * 1024);
                const password = 'TestPassword123!';

                const { time: totalTime } = await measureAsyncPerformance(async () => {
                    // Full processing pipeline
                    const encrypted = await crypto.encryptBeforeDNA(largeData, password);
                    const encoded = codec.encodeWithErrorCorrection(encrypted);
                    const decoded = codec.decodeWithErrorCorrection(encoded);
                    const decrypted = await crypto.decryptAfterDNA(decoded, password);

                    // Verify integrity
                    if (decrypted.length !== largeData.length) {
                        throw new Error('Large file processing integrity check failed');
                    }
                });

                const throughput = (largeData.length / 1024 / 1024 / (totalTime / 1000)).toFixed(2); // MB/s
                log(`Large file performance: ${totalTime.toFixed(2)}ms for 5MB (${throughput} MB/s)`, 'success', 'performance');
            }, 'performance');
        }

        async function testConcurrentOperations() {
            await runTest('Concurrent Operations', async () => {
                const crypto = new AdvancedCryptoEngine();
                const concurrentCount = 5;
                const dataSize = 10000; // 10KB each

                const operations = [];
                for (let i = 0; i < concurrentCount; i++) {
                    const data = generateRandomData(dataSize);
                    const password = `TestPassword${i}!`;

                    operations.push(
                        crypto.encryptBeforeDNA(data, password).then(encrypted =>
                            crypto.decryptAfterDNA(encrypted, password)
                        )
                    );
                }

                const { time } = await measureAsyncPerformance(async () => {
                    const results = await Promise.all(operations);

                    // Verify all operations completed successfully
                    if (results.length !== concurrentCount) {
                        throw new Error('Not all concurrent operations completed');
                    }
                });

                log(`Concurrent operations: ${concurrentCount} operations in ${time.toFixed(2)}ms`, 'success', 'performance');
            }, 'performance');
        }

        async function testBrowserCompatibility() {
            await runTest('Browser Compatibility', async () => {
                const features = {
                    'Web Crypto API': !!window.crypto && !!window.crypto.subtle,
                    'IndexedDB': !!window.indexedDB,
                    'File API': !!window.File && !!window.FileReader,
                    'Blob API': !!window.Blob,
                    'ArrayBuffer': !!window.ArrayBuffer,
                    'Uint8Array': !!window.Uint8Array,
                    'TextEncoder': !!window.TextEncoder,
                    'TextDecoder': !!window.TextDecoder,
                    'Performance API': !!window.performance,
                    'Local Storage': !!window.localStorage
                };

                const supported = Object.values(features).filter(Boolean).length;
                const total = Object.keys(features).length;

                if (supported < total * 0.8) {
                    throw new Error(`Browser compatibility issues: ${supported}/${total} features supported`);
                }

                log(`Browser compatibility: ${supported}/${total} features supported`, 'success', 'performance');
            }, 'performance');
        }

        async function testMobilePerformance() {
            await runTest('Mobile Performance', async () => {
                // Simulate mobile constraints
                const isMobile = /Android|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent);
                const isLowPower = navigator.hardwareConcurrency && navigator.hardwareConcurrency <= 2;

                const crypto = new AdvancedCryptoEngine();
                const testData = generateRandomData(isMobile || isLowPower ? 5000 : 10000); // Smaller data for mobile
                const password = 'TestPassword123!';

                const { time } = await measureAsyncPerformance(async () => {
                    const encrypted = await crypto.encryptBeforeDNA(testData, password);
                    return await crypto.decryptAfterDNA(encrypted, password);
                });

                const maxAcceptableTime = isMobile || isLowPower ? 2000 : 1000; // 2s for mobile, 1s for desktop

                if (time > maxAcceptableTime) {
                    log(`Mobile performance warning: ${time.toFixed(2)}ms (target: ${maxAcceptableTime}ms)`, 'warning', 'performance');
                } else {
                    log(`Mobile performance: ${time.toFixed(2)}ms (target: ${maxAcceptableTime}ms)`, 'success', 'performance');
                }
            }, 'performance');
        }

        async function testLoadTime() {
            await runTest('Load Time', async () => {
                if (!performance.timing) {
                    log('Navigation timing API not available', 'warning', 'performance');
                    return;
                }

                const timing = performance.timing;
                const loadTime = timing.loadEventEnd - timing.navigationStart;
                const domReady = timing.domContentLoadedEventEnd - timing.navigationStart;

                log(`Load time: ${loadTime}ms, DOM ready: ${domReady}ms`, 'success', 'performance');

                if (loadTime > 5000) {
                    log('Load time exceeds 5 seconds', 'warning', 'performance');
                }
            }, 'performance');
        }

        async function testStressTest() {
            await runTest('Stress Test', async () => {
                const crypto = new AdvancedCryptoEngine();
                const codec = new DNACodec();
                const iterations = 50;
                const dataSize = 1000; // 1KB per iteration

                let successCount = 0;
                let errorCount = 0;

                const { time } = await measureAsyncPerformance(async () => {
                    for (let i = 0; i < iterations; i++) {
                        try {
                            const data = generateRandomData(dataSize);
                            const password = `StressTest${i}!`;

                            const encrypted = await crypto.encryptBeforeDNA(data, password);
                            const encoded = codec.encodeWithErrorCorrection(encrypted);
                            const decoded = codec.decodeWithErrorCorrection(encoded);
                            const decrypted = await crypto.decryptAfterDNA(decoded, password);

                            if (decrypted.length === data.length) {
                                successCount++;
                            } else {
                                errorCount++;
                            }
                        } catch (error) {
                            errorCount++;
                        }
                    }
                });

                const successRate = (successCount / iterations * 100).toFixed(2);

                if (successRate < 95) {
                    throw new Error(`Stress test failed: ${successRate}% success rate`);
                }

                log(`Stress test: ${successCount}/${iterations} successful (${successRate}%) in ${time.toFixed(2)}ms`, 'success', 'performance');
            }, 'performance');
        }

        // ==================== INTEGRATION TESTS ====================

        async function testFullEncryptionFlow() {
            await runTest('Full Encryption Flow', async () => {
                const fileHandler = new AdvancedFileHandler();
                const crypto = new AdvancedCryptoEngine();
                const codec = new DNACodec();
                const storage = new StorageManager();
                await storage.initialize();

                // Create test file
                const testContent = 'This is a test file for full encryption flow testing.';
                const testFile = new Blob([testContent], { type: 'text/plain' });
                const password = 'FullFlowTest123!';

                // Full encryption flow
                const fileData = await fileHandler.readFileAsArrayBuffer(testFile);
                const encrypted = await crypto.encryptBeforeDNA(new Uint8Array(fileData), password);
                const dnaEncoded = codec.encodeWithErrorCorrection(encrypted);

                // Store metadata
                const metadata = {
                    id: 'full-flow-test-' + Date.now(),
                    filename: 'test.txt',
                    size: testFile.size,
                    timestamp: new Date(),
                    dnaSequence: dnaEncoded
                };

                await storage.storeEncryption(metadata);

                // Full decryption flow
                const retrieved = await storage.getEncryption(metadata.id);
                const dnaDecoded = codec.decodeWithErrorCorrection(retrieved.dnaSequence);
                const decrypted = await crypto.decryptAfterDNA(dnaDecoded, password);
                const decryptedText = new TextDecoder().decode(decrypted);

                if (decryptedText !== testContent) {
                    throw new Error('Full encryption flow integrity check failed');
                }

                // Cleanup
                await storage.deleteEncryption(metadata.id);

                log('Full encryption/decryption flow completed successfully', 'success', 'integration');
            }, 'integration');
        }

        async function testEndToEndEncryption() {
            await runTest('End-to-End Encryption', async () => {
                const app = new DNAEncryptionApp();
                await app.initialize();

                // Simulate user workflow
                const testData = 'Sensitive information for end-to-end testing';
                const password = 'E2ETest123!';

                // Encrypt
                const encryptResult = await app.encryptData(testData, password);

                if (!encryptResult || !encryptResult.dnaSequence) {
                    throw new Error('End-to-end encryption failed');
                }

                // Decrypt
                const decryptResult = await app.decryptData(encryptResult.dnaSequence, password);

                if (decryptResult !== testData) {
                    throw new Error('End-to-end decryption failed');
                }

                log('End-to-end encryption completed successfully', 'success', 'integration');
            }, 'integration');
        }

        async function testMultipleFiles() {
            await runTest('Multiple Files Processing', async () => {
                const fileHandler = new AdvancedFileHandler();
                const crypto = new AdvancedCryptoEngine();
                const codec = new DNACodec();

                const testFiles = [
                    { name: 'file1.txt', content: 'Content of file 1' },
                    { name: 'file2.txt', content: 'Content of file 2' },
                    { name: 'file3.txt', content: 'Content of file 3' }
                ];

                const password = 'MultiFileTest123!';
                const results = [];

                // Process multiple files
                for (const testFile of testFiles) {
                    const blob = new Blob([testFile.content], { type: 'text/plain' });
                    const fileData = await fileHandler.readFileAsArrayBuffer(blob);
                    const encrypted = await crypto.encryptBeforeDNA(new Uint8Array(fileData), password);
                    const dnaEncoded = codec.encodeWithErrorCorrection(encrypted);

                    results.push({
                        name: testFile.name,
                        original: testFile.content,
                        dnaSequence: dnaEncoded
                    });
                }

                // Verify all files
                for (const result of results) {
                    const dnaDecoded = codec.decodeWithErrorCorrection(result.dnaSequence);
                    const decrypted = await crypto.decryptAfterDNA(dnaDecoded, password);
                    const decryptedText = new TextDecoder().decode(decrypted);

                    if (decryptedText !== result.original) {
                        throw new Error(`Multiple files test failed for ${result.name}`);
                    }
                }

                log(`Multiple files processing: ${results.length} files processed successfully`, 'success', 'integration');
            }, 'integration');
        }

        async function testDifferentFileTypes() {
            await runTest('Different File Types', async () => {
                const fileHandler = new AdvancedFileHandler();
                const crypto = new AdvancedCryptoEngine();
                const codec = new DNACodec();

                const fileTypes = [
                    { type: 'text/plain', content: 'Plain text content', name: 'test.txt' },
                    { type: 'application/json', content: '{"key": "value"}', name: 'test.json' },
                    { type: 'text/html', content: '<html><body>HTML content</body></html>', name: 'test.html' },
                    { type: 'text/css', content: 'body { color: red; }', name: 'test.css' }
                ];

                const password = 'FileTypesTest123!';

                for (const fileType of fileTypes) {
                    const blob = new Blob([fileType.content], { type: fileType.type });
                    const fileData = await fileHandler.readFileAsArrayBuffer(blob);
                    const encrypted = await crypto.encryptBeforeDNA(new Uint8Array(fileData), password);
                    const dnaEncoded = codec.encodeWithErrorCorrection(encrypted);
                    const dnaDecoded = codec.decodeWithErrorCorrection(dnaEncoded);
                    const decrypted = await crypto.decryptAfterDNA(dnaDecoded, password);
                    const decryptedText = new TextDecoder().decode(decrypted);

                    if (decryptedText !== fileType.content) {
                        throw new Error(`File type test failed for ${fileType.type}`);
                    }
                }

                log(`Different file types: ${fileTypes.length} types processed successfully`, 'success', 'integration');
            }, 'integration');
        }

        async function testErrorRecovery() {
            await runTest('Error Recovery', async () => {
                const crypto = new AdvancedCryptoEngine();
                const codec = new DNACodec();

                const testData = generateRandomData(1000);
                const password = 'ErrorRecoveryTest123!';

                // Encrypt and encode
                const encrypted = await crypto.encryptBeforeDNA(testData, password);
                const dnaEncoded = codec.encodeWithErrorCorrection(encrypted);

                // Introduce errors in DNA sequence
                const corruptedDNA = introduceErrors(dnaEncoded, 5);

                // Test error correction
                try {
                    const corrected = codec.decodeWithErrorCorrection(corruptedDNA);
                    const decrypted = await crypto.decryptAfterDNA(corrected, password);

                    // Verify recovery
                    let matchCount = 0;
                    for (let i = 0; i < Math.min(testData.length, decrypted.length); i++) {
                        if (testData[i] === decrypted[i]) matchCount++;
                    }

                    const recoveryRate = (matchCount / testData.length * 100).toFixed(2);

                    if (recoveryRate < 95) {
                        throw new Error(`Error recovery rate too low: ${recoveryRate}%`);
                    }

                    log(`Error recovery: ${recoveryRate}% data recovered from corrupted DNA`, 'success', 'integration');
                } catch (error) {
                    if (error.message.includes('recovery rate')) {
                        throw error;
                    }
                    log('Error recovery test: Corruption too severe for recovery (expected behavior)', 'success', 'integration');
                }
            }, 'integration');
        }

        async function testUserWorkflow() {
            await runTest('User Workflow', async () => {
                const uiManager = new UIManager();
                const storage = new StorageManager();
                await uiManager.initialize();
                await storage.initialize();

                // Simulate complete user workflow
                const steps = [
                    'File selection',
                    'Password entry',
                    'Encryption process',
                    'DNA encoding',
                    'Storage',
                    'Retrieval',
                    'DNA decoding',
                    'Decryption process',
                    'File download'
                ];

                for (let i = 0; i < steps.length; i++) {
                    const step = steps[i];
                    uiManager.updateProgress((i + 1) / steps.length * 100, `Processing: ${step}`);
                    await sleep(100);
                }

                uiManager.hideProgress();
                uiManager.showNotification('Workflow completed successfully', 'success', 'Integration Test');

                log(`User workflow simulation: ${steps.length} steps completed`, 'success', 'integration');
            }, 'integration');
        }

        async function testDataPersistence() {
            await runTest('Data Persistence', async () => {
                const storage = new StorageManager();
                await storage.initialize();

                const testData = {
                    id: 'persistence-test-' + Date.now(),
                    filename: 'persistent.txt',
                    size: 1000,
                    timestamp: new Date(),
                    metadata: { persistent: true }
                };

                // Store data
                await storage.storeEncryption(testData);

                // Simulate page reload by creating new storage instance
                const newStorage = new StorageManager();
                await newStorage.initialize();

                // Retrieve data
                const retrieved = await newStorage.getEncryption(testData.id);

                if (!retrieved || retrieved.filename !== testData.filename) {
                    throw new Error('Data persistence test failed');
                }

                // Cleanup
                await newStorage.deleteEncryption(testData.id);

                log('Data persistence across sessions verified', 'success', 'integration');
            }, 'integration');
        }

        async function testCrossTabCommunication() {
            await runTest('Cross-Tab Communication', async () => {
                // Test localStorage events for cross-tab communication
                const testKey = 'cross-tab-test-' + Date.now();
                const testValue = 'Cross-tab communication test';

                let eventReceived = false;

                const handleStorageEvent = (event) => {
                    if (event.key === testKey && event.newValue === testValue) {
                        eventReceived = true;
                    }
                };

                window.addEventListener('storage', handleStorageEvent);

                // Simulate cross-tab communication
                localStorage.setItem(testKey, testValue);

                // Wait for event
                await sleep(100);

                window.removeEventListener('storage', handleStorageEvent);
                localStorage.removeItem(testKey);

                // Note: storage events don't fire in the same tab that made the change
                log('Cross-tab communication mechanism available', 'success', 'integration');
            }, 'integration');
        }

        async function testOfflineMode() {
            await runTest('Offline Mode', async () => {
                // Test offline functionality
                const originalOnline = navigator.onLine;

                // Mock offline state
                Object.defineProperty(navigator, 'onLine', {
                    writable: true,
                    value: false
                });

                const crypto = new AdvancedCryptoEngine();
                const codec = new DNACodec();

                // Test that core functionality works offline
                const testData = generateRandomData(1000);
                const password = 'OfflineTest123!';

                const encrypted = await crypto.encryptBeforeDNA(testData, password);
                const dnaEncoded = codec.encodeWithErrorCorrection(encrypted);
                const dnaDecoded = codec.decodeWithErrorCorrection(dnaEncoded);
                const decrypted = await crypto.decryptAfterDNA(dnaDecoded, password);

                // Verify offline functionality
                if (decrypted.length !== testData.length) {
                    throw new Error('Offline mode functionality failed');
                }

                // Restore online state
                Object.defineProperty(navigator, 'onLine', {
                    writable: true,
                    value: originalOnline
                });

                log('Offline mode functionality verified', 'success', 'integration');
            }, 'integration');
        }

        async function testCompleteIntegration() {
            await runTest('Complete Integration', async () => {
                // Test complete system integration
                const app = new DNAEncryptionApp();
                const storage = new StorageManager();
                const uiManager = new UIManager();

                await app.initialize();
                await storage.initialize();
                await uiManager.initialize();

                // Test all components working together
                const testContent = 'Complete integration test content';
                const password = 'CompleteIntegrationTest123!';

                // Show progress
                uiManager.showProgress('Starting complete integration test...', 0);

                // Encrypt
                uiManager.updateProgress(25, 'Encrypting data...');
                const encryptResult = await app.encryptData(testContent, password);

                // Store
                uiManager.updateProgress(50, 'Storing encrypted data...');
                const metadata = {
                    id: 'complete-integration-' + Date.now(),
                    filename: 'integration-test.txt',
                    size: testContent.length,
                    timestamp: new Date(),
                    dnaSequence: encryptResult.dnaSequence
                };
                await storage.storeEncryption(metadata);

                // Retrieve
                uiManager.updateProgress(75, 'Retrieving data...');
                const retrieved = await storage.getEncryption(metadata.id);

                // Decrypt
                uiManager.updateProgress(90, 'Decrypting data...');
                const decryptResult = await app.decryptData(retrieved.dnaSequence, password);

                // Verify
                uiManager.updateProgress(100, 'Verification complete!');
                if (decryptResult !== testContent) {
                    throw new Error('Complete integration test failed');
                }

                // Cleanup
                await storage.deleteEncryption(metadata.id);
                uiManager.hideProgress();
                uiManager.showNotification('Complete integration test passed!', 'success', 'Integration');

                log('Complete system integration test passed', 'success', 'integration');
            }, 'integration');
        }

        // ==================== MAIN CONTROL FUNCTIONS ====================

        async function runAllTests() {
            log('🚀 شروع اجرای همه تست‌ها | Starting all tests execution', 'info');
            testStats.total = 0;
            testStats.passed = 0;
            testStats.failed = 0;

            // Clear all results
            document.querySelectorAll('.category-results').forEach(container => {
                container.innerHTML = '';
            });

            try {
                // Module Loading Tests (5 tests)
                await testModuleLoading();
                await testDependencies();
                await testModuleExports();
                await testInitialization();
                await testVersions();

                // DNA Codec Tests (10 tests)
                await testBasicEncoding();
                await testBasicDecoding();
                await testErrorCorrection();
                await testCompression();
                await testChecksum();
                await testMetadata();
                await testValidation();
                await testLargeSequences();
                await testCorruptedDNA();
                await testDNAPerformance();

                // Crypto Engine Tests (10 tests)
                await testAESEncryption();
                await testAESDecryption();
                await testPBKDF2KeyDerivation();
                await testSaltGeneration();
                await testIVGeneration();
                await testPasswordStrength();
                await testSecureRandomness();
                await testHashFunctions();
                await testCryptoPerformance();
                await testWrongPassword();

                // File Handler Tests (10 tests)
                await testFileValidation();
                await testMetadataExtraction();
                await testFileTypeDetection();
                await testLargeFileHandling();
                await testFileChunking();
                await testDNAFileExtensions();
                await testDownloadMechanism();
                await testFileSizeLimits();
                await testBinaryFiles();
                await testTextFiles();

                // Storage Manager Tests (10 tests)
                await testStorageInitialization();
                await testDataStorage();
                await testDataRetrieval();
                await testDataDeletion();
                await testStorageQuota();
                await testIndexedDBOperations();
                await testStorageEncryption();
                await testStorageBackup();
                await testStorageCleanup();
                await testStoragePerformance();

                // UI Manager Tests (10 tests)
                await testUIInitialization();
                await testThemeSwitch();
                await testLanguageSwitch();
                await testNotifications();
                await testProgressModal();
                await testTabSwitching();
                await testKeyboardShortcuts();
                await testResponsiveDesign();
                await testAccessibility();
                await testUIAnimations();

                // Advanced Features Tests (10 tests)
                await testAdvancedFeatures();
                await test3DDNA();
                await testGSAPAnimations();
                await testSoundEffects();
                await testPreferences();
                await testAdvancedSettings();
                await testPerformanceMode();
                await testFullscreenMode();
                await testExportImport();
                await testAdvancedAnalytics();

                // Security Tests (10 tests)
                await testPasswordSecurity();
                await testDataIntegrity();
                await testEncryptionStrength();
                await testBruteForceProtection();
                await testMemoryLeaks();
                await testSecureStorage();
                await testInputSanitization();
                await testXSSProtection();
                await testCSRFProtection();
                await testSecureHeaders();

                // Performance Tests (10 tests)
                await testEncryptionSpeed();
                await testDecryptionSpeed();
                await testMemoryUsage();
                await testCPUUsage();
                await testLargeFilePerformance();
                await testConcurrentOperations();
                await testBrowserCompatibility();
                await testMobilePerformance();
                await testLoadTime();
                await testStressTest();

                // Integration Tests (10 tests)
                await testFullEncryptionFlow();
                await testEndToEndEncryption();
                await testMultipleFiles();
                await testDifferentFileTypes();
                await testErrorRecovery();
                await testUserWorkflow();
                await testDataPersistence();
                await testCrossTabCommunication();
                await testOfflineMode();
                await testCompleteIntegration();

                const successRate = (testStats.passed / testStats.total * 100).toFixed(2);
                log(`🎉 همه تست‌ها تکمیل شد | All tests completed: ${testStats.passed}/${testStats.total} passed (${successRate}%)`, 'success');

            } catch (error) {
                log(`❌ خطا در اجرای تست‌ها | Error running tests: ${error.message}`, 'error');
            }
        }

        async function runCoreTests() {
            log('🔧 اجرای تست‌های اصلی | Running core tests', 'info');

            // Module Loading Tests
            await testModuleLoading();
            await testDependencies();
            await testModuleExports();
            await testInitialization();
            await testVersions();

            // DNA Codec Tests
            await testBasicEncoding();
            await testBasicDecoding();
            await testErrorCorrection();
            await testCompression();
            await testChecksum();

            // Crypto Engine Tests
            await testAESEncryption();
            await testAESDecryption();
            await testPBKDF2KeyDerivation();
            await testSaltGeneration();
            await testIVGeneration();

            log('✅ تست‌های اصلی تکمیل شد | Core tests completed', 'success');
        }

        async function runSecurityTests() {
            log('🔒 اجرای تست‌های امنیتی | Running security tests', 'info');

            await testPasswordSecurity();
            await testDataIntegrity();
            await testEncryptionStrength();
            await testBruteForceProtection();
            await testMemoryLeaks();
            await testSecureStorage();
            await testInputSanitization();
            await testXSSProtection();
            await testCSRFProtection();
            await testSecureHeaders();

            log('🛡️ تست‌های امنیتی تکمیل شد | Security tests completed', 'success');
        }

        async function runPerformanceTests() {
            log('⚡ اجرای تست‌های عملکرد | Running performance tests', 'info');

            await testEncryptionSpeed();
            await testDecryptionSpeed();
            await testMemoryUsage();
            await testCPUUsage();
            await testLargeFilePerformance();
            await testConcurrentOperations();
            await testBrowserCompatibility();
            await testMobilePerformance();
            await testLoadTime();
            await testStressTest();

            log('🚀 تست‌های عملکرد تکمیل شد | Performance tests completed', 'success');
        }

        // Helper function to introduce errors in DNA sequence (complete implementation)
        function introduceErrors(dnaSequence, errorCount) {
            if (!dnaSequence || errorCount <= 0) return dnaSequence;

            const sequence = dnaSequence.split('');
            const nucleotides = ['A', 'T', 'C', 'G'];
            const errors = Math.min(errorCount, sequence.length);

            for (let i = 0; i < errors; i++) {
                const randomIndex = Math.floor(Math.random() * sequence.length);
                const currentNucleotide = sequence[randomIndex];
                const availableNucleotides = nucleotides.filter(n => n !== currentNucleotide);
                sequence[randomIndex] = availableNucleotides[Math.floor(Math.random() * availableNucleotides.length)];
            }

            return sequence.join('');
        }

        // Clear all test results
        function clearAllResults() {
            // Reset statistics
            testStats.total = 0;
            testStats.passed = 0;
            testStats.failed = 0;
            updateStats();

            // Clear all result containers
            document.querySelectorAll('.test-results').forEach(container => {
                container.innerHTML = '';
            });

            // Clear category results
            document.querySelectorAll('.category-results').forEach(container => {
                container.innerHTML = '';
            });

            // Clear main log
            const logContainer = document.getElementById('testLog');
            if (logContainer) {
                logContainer.innerHTML = '';
            }

            log('🗑️ همه نتایج پاک شد | All results cleared', 'info');
        }

        // Auto-run basic tests
        window.addEventListener('load', () => {
            log('📄 صفحه تست بارگذاری شد | Test page loaded', 'info');
            setTimeout(() => {
                testModuleLoading();
            }, 500);
        });
    </script>
</body>
</html>
