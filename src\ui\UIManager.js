class UIManager {
    constructor() {
        this.progressModal = null;
        this.progressRing = null;
        this.progressPercentage = null;
        this.progressText = null;
        this.progressSteps = null;
        this.notificationContainer = null;
        this.currentTab = 'dashboard';
        this.sidebar = null;
        this.themeToggle = null;
        this.currentTheme = 'dark';
        this.currentLanguage = 'fa';
        this.notifications = [];
        this.charts = {};

        // Language translations
        this.translations = {
            fa: {
                processing: 'در حال پردازش...',
                encrypting: 'در حال رمزگذاری...',
                decrypting: 'در حال رمزگشایی...',
                encoding: 'در حال تبدیل به DNA...',
                decoding: 'در حال تبدیل از DNA...',
                complete: 'تکمیل شد',
                error: 'خطا',
                success: 'موفق',
                fileSelected: 'فایل انتخاب شد',
                encryptionComplete: 'رمزگذاری با موفقیت انجام شد!',
                decryptionComplete: 'رمزگشایی با موفقیت انجام شد! دانلود آغاز شد.',
                systemReady: 'سیستم آماده است',
                dashboard: 'داشبورد',
                encrypt: 'رمزگذاری',
                decrypt: 'رمزگشایی',
                analytics: 'آنالیز',
                history: 'تاریخچه',
                settings: 'تنظیمات'
            },
            en: {
                processing: 'Processing...',
                encrypting: 'Encrypting...',
                decrypting: 'Decrypting...',
                encoding: 'Converting to DNA...',
                decoding: 'Converting from DNA...',
                complete: 'Complete',
                error: 'Error',
                success: 'Success',
                fileSelected: 'File selected',
                encryptionComplete: 'Encryption completed successfully!',
                decryptionComplete: 'Decryption completed successfully! Download started.',
                systemReady: 'System ready',
                dashboard: 'Dashboard',
                encrypt: 'Encrypt',
                decrypt: 'Decrypt',
                analytics: 'Analytics',
                history: 'History',
                settings: 'Settings'
            }
        };

        // Animation settings
        this.animationEnabled = true;
        this.soundEnabled = true;

        // Keyboard shortcuts
        this.shortcuts = {
            'ctrl+o': () => this.triggerFileSelect(),
            'ctrl+e': () => this.switchTab('encrypt'),
            'ctrl+d': () => this.switchTab('decrypt'),
            'ctrl+h': () => this.switchTab('history'),
            'ctrl+,': () => this.switchTab('settings'),
            'f11': () => this.toggleFullscreen(),
            'escape': () => this.closeModals()
        };
    }

    async initialize() {
        try {
            // Show loading screen
            this.showLoadingScreen();

            // Initialize DOM elements
            this.initializeDOMElements();

            // Load saved settings
            this.loadSettings();

            // Setup event listeners
            this.setupEventListeners();

            // Initialize theme
            this.initializeTheme();

            // Initialize language
            this.initializeLanguage();

            // Initialize charts
            await this.initializeCharts();

            // Setup keyboard shortcuts
            this.setupKeyboardShortcuts();

            // Setup animations
            this.setupAnimations();

            // Setup file handling
            this.setupFileHandling();

            // Load saved tab
            this.loadSavedTab();

            // Hide loading screen
            setTimeout(() => {
                this.hideLoadingScreen();
                this.showNotification(this.t('systemReady'), 'success');
            }, 1500);

        } catch (error) {
            console.error('UI initialization error:', error);
            this.showNotification('خطا در راه‌اندازی رابط کاربری', 'error');
        }
    }

    initializeDOMElements() {
        this.progressModal = document.getElementById('progressModal');
        this.progressRing = document.querySelector('.progress-ring-circle');
        this.progressPercentage = document.getElementById('progressPercentage');
        this.progressText = document.getElementById('progressText');
        this.progressSteps = document.querySelectorAll('.step');
        this.notificationContainer = document.getElementById('notificationContainer');
        this.sidebar = document.getElementById('sidebar');
        this.themeToggle = document.getElementById('themeToggle');
    }

    loadSettings() {
        // Load theme
        this.currentTheme = localStorage.getItem('dna-app-theme') || 'dark';

        // Load language
        this.currentLanguage = localStorage.getItem('dna-app-language') || 'fa';

        // Load animation setting
        const savedAnimations = localStorage.getItem('dna-app-animations');
        if (savedAnimations !== null) {
            this.animationEnabled = savedAnimations === 'true';
        }

        // Load sound setting
        const savedSounds = localStorage.getItem('dna-app-sounds');
        if (savedSounds !== null) {
            this.soundEnabled = savedSounds === 'true';
        }
    }

    loadSavedTab() {
        const savedTab = localStorage.getItem('dna-app-current-tab') || 'dashboard';
        this.switchTab(savedTab);
    }

    // Translation helper
    t(key) {
        return this.translations[this.currentLanguage]?.[key] || this.translations.fa[key] || key;
    }

    showLoadingScreen() {
        const loadingScreen = document.getElementById('loadingScreen');
        if (loadingScreen) {
            loadingScreen.classList.remove('hidden');

            // Animate loading progress
            const loadingBar = document.querySelector('.loading-bar');
            if (loadingBar) {
                setTimeout(() => {
                    loadingBar.style.width = '100%';
                }, 500);
            }
        }
    }

    hideLoadingScreen() {
        const loadingScreen = document.getElementById('loadingScreen');
        if (loadingScreen) {
            loadingScreen.classList.add('hidden');
            setTimeout(() => {
                loadingScreen.style.display = 'none';
            }, 500);
        }
    }

    setupEventListeners() {
        // Sidebar toggle
        const sidebarToggle = document.getElementById('sidebarToggle');
        const mobileMenuToggle = document.getElementById('mobileMenuToggle');

        if (sidebarToggle) {
            sidebarToggle.addEventListener('click', (e) => {
                e.preventDefault();
                this.toggleSidebar();
                this.updateSidebarOpenBtnVisibility();
            });
        }

        if (mobileMenuToggle) {
            mobileMenuToggle.addEventListener('click', (e) => {
                e.preventDefault();
                this.toggleSidebar();
                this.updateSidebarOpenBtnVisibility();
            });
        }

        // Add sidebar open button to ensure it can be reopened
        const sidebarOpenBtn = document.getElementById('sidebarOpenBtn');
        if (sidebarOpenBtn) {
            sidebarOpenBtn.addEventListener('click', (e) => {
                e.preventDefault();
                if (this.sidebar && !this.sidebar.classList.contains('show')) {
                    this.toggleSidebar();
                    this.updateSidebarOpenBtnVisibility();
                }
            });
        }

        // To-Do List add button
        const addTodoBtn = document.getElementById('addTodoBtn');
        const todoInput = document.getElementById('todoInput');
        if (addTodoBtn && todoInput) {
            addTodoBtn.addEventListener('click', () => {
                this.addTodoItem(todoInput.value);
                todoInput.value = '';
            });

            todoInput.addEventListener('keypress', (e) => {
                if (e.key === 'Enter') {
                    this.addTodoItem(todoInput.value);
                    todoInput.value = '';
                }
            });
        }

        // To-Do List container for event delegation
        const todoList = document.getElementById('todoList');
        if (todoList) {
            todoList.addEventListener('click', (e) => {
                if (e.target.classList.contains('todo-delete')) {
                    const item = e.target.closest('li');
                    if (item) {
                        this.removeTodoItem(item.dataset.id);
                    }
                } else if (e.target.classList.contains('todo-checkbox')) {
                    const item = e.target.closest('li');
                    if (item) {
                        this.toggleTodoItem(item.dataset.id, e.target.checked);
                    }
                }
            });
        }

        // Navigation
        const navItems = document.querySelectorAll('.nav-item');
        navItems.forEach(item => {
            item.addEventListener('click', (e) => {
        });

    }

    addTodoItem(text) {
        if (!text.trim()) return;

        const todoList = document.getElementById('todoList');
        if (!todoList) return;

        const id = Date.now().toString();
        const li = document.createElement('li');
        li.dataset.id = id;
        li.className = 'todo-item';

        li.innerHTML = `
            <label>
                <input type="checkbox" class="todo-checkbox">
                <span class="todo-text">${text}</span>
            </label>
            <button class="todo-delete" title="حذف">
                <i class="fas fa-trash"></i>
            </button>
        `;

        todoList.appendChild(li);
        this.showNotification('کار جدید به لیست اضافه شد', 'success');
    }

    removeTodoItem(id) {
        const todoList = document.getElementById('todoList');
        if (!todoList) return;

        const item = todoList.querySelector(`li[data-id="${id}"]`);
        if (item) {
            todoList.removeChild(item);
            this.showNotification('کار از لیست حذف شد', 'info');
        }
    }

    toggleTodoItem(id, completed) {
        const todoList = document.getElementById('todoList');
        if (!todoList) return;

        const item = todoList.querySelector(`li[data-id="${id}"]`);
        if (item) {
            if (completed) {
                item.classList.add('completed');
            } else {
                item.classList.remove('completed');
            }
        }
    }
                e.preventDefault();
                const tab = item.dataset.tab;
                if (tab) {
                    this.switchTab(tab);
                }
            });
        });

        // Theme toggle
        if (this.themeToggle) {
            this.themeToggle.addEventListener('click', () => this.toggleTheme());
        }

        // Theme selector buttons
        const themeOptions = document.querySelectorAll('.theme-option');
        themeOptions.forEach(option => {
            option.addEventListener('click', () => {
                const theme = option.dataset.theme;
                this.setTheme(theme);
            });
        });

        // Language selector
        const languageSelect = document.getElementById('language');
        if (languageSelect) {
            languageSelect.addEventListener('change', (e) => {
                this.setLanguage(e.target.value);
            });
        }

        // Settings toggles
        this.setupSettingsToggles();

        // Fullscreen toggle
        const fullscreenBtn = document.getElementById('fullscreenBtn');
        if (fullscreenBtn) {
            fullscreenBtn.addEventListener('click', () => this.toggleFullscreen());
        }

        // Modal close buttons
        const modalCloses = document.querySelectorAll('.modal-close, .progress-close');
        modalCloses.forEach(btn => {
            btn.addEventListener('click', () => this.closeModals());
        });

        // Advanced settings toggle
        const advancedToggle = document.getElementById('advancedToggle');
        if (advancedToggle) {
            advancedToggle.addEventListener('click', () => this.toggleAdvancedSettings());
        }

        // Password generator
        const generatePassword = document.getElementById('generatePassword');
        if (generatePassword) {
            generatePassword.addEventListener('click', () => this.generateSecurePassword());
        }

        // Password toggles
        this.setupPasswordToggles();

        // DNA tools
        this.setupDNATools();

        // File drop zone
        this.setupFileDropZone();

        // Quick action buttons
        this.setupQuickActions();

        // Window resize
        window.addEventListener('resize', () => this.handleResize());

        // Escape key for modals
        document.addEventListener('keydown', (e) => {
            if (e.key === 'Escape') {
                this.closeModals();
            }
        });
    }

    initializeTheme() {
        this.setTheme(this.currentTheme);
    }

    setTheme(theme) {
        this.currentTheme = theme;
        document.body.className = document.body.className.replace(/theme-\w+/g, '') + ` theme-${theme}`;
        localStorage.setItem('dna-app-theme', theme);

        // Update theme selector
        const themeOptions = document.querySelectorAll('.theme-option');
        themeOptions.forEach(option => {
            option.classList.toggle('active', option.dataset.theme === theme);
        });

        // Update theme toggle icon
        if (this.themeToggle) {
            const icon = this.themeToggle.querySelector('i');
            if (icon) {
                icon.className = theme === 'dark' ? 'fas fa-sun' : 'fas fa-moon';
            }
        }
    }

    toggleTheme() {
        const newTheme = this.currentTheme === 'dark' ? 'light' : 'dark';
        this.setTheme(newTheme);
    }

    initializeLanguage() {
        this.setLanguage(this.currentLanguage);
    }

    setLanguage(language) {
        this.currentLanguage = language;
        localStorage.setItem('dna-app-language', language);

        // Update document attributes
        document.documentElement.lang = language;
        document.body.setAttribute('data-lang', language);

        // Update direction for RTL languages
        if (language === 'fa' || language === 'ar') {
            document.documentElement.dir = 'rtl';
        } else {
            document.documentElement.dir = 'ltr';
        }

        // Update language selector
        const languageSelect = document.getElementById('language');
        if (languageSelect) {
            languageSelect.value = language;
        }

        // Update UI text
        this.updateUIText();

        this.showNotification(this.t('success'), 'success');
    }

    updateUIText() {
        // Update navigation items
        const navItems = {
            'dashboard': this.t('dashboard'),
            'encrypt': this.t('encrypt'),
            'decrypt': this.t('decrypt'),
            'analytics': this.t('analytics'),
            'history': this.t('history'),
            'settings': this.t('settings')
        };

        Object.entries(navItems).forEach(([tab, text]) => {
            const navItem = document.querySelector(`[data-tab="${tab}"] span`);
            if (navItem) {
                navItem.textContent = text;
            }
        });
    }

    async initializeCharts() {
        // Initialize activity chart
        const activityCtx = document.getElementById('activityChart');
        if (activityCtx && typeof Chart !== 'undefined') {
            this.charts.activity = new Chart(activityCtx, {
                type: 'line',
                data: {
                    labels: ['شنبه', 'یکشنبه', 'دوشنبه', 'سه‌شنبه', 'چهارشنبه', 'پنج‌شنبه', 'جمعه'],
                    datasets: [{
                        label: 'رمزگذاری',
                        data: [0, 0, 0, 0, 0, 0, 0],
                        borderColor: '#6366f1',
                        backgroundColor: 'rgba(99, 102, 241, 0.1)',
                        tension: 0.4
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            display: false
                        }
                    },
                    scales: {
                        y: {
                            beginAtZero: true,
                            grid: {
                                color: 'rgba(148, 163, 184, 0.1)'
                            }
                        },
                        x: {
                            grid: {
                                color: 'rgba(148, 163, 184, 0.1)'
                            }
                        }
                    }
                }
            });
        }

        // Initialize performance chart
        const performanceCtx = document.getElementById('performanceChart');
        if (performanceCtx && typeof Chart !== 'undefined') {
            this.charts.performance = new Chart(performanceCtx, {
                type: 'bar',
                data: {
                    labels: ['رمزگذاری', 'رمزگشایی', 'فشرده‌سازی', 'تصحیح خطا'],
                    datasets: [{
                        label: 'زمان (میلی‌ثانیه)',
                        data: [0, 0, 0, 0],
                        backgroundColor: [
                            'rgba(99, 102, 241, 0.8)',
                            'rgba(139, 92, 246, 0.8)',
                            'rgba(6, 182, 212, 0.8)',
                            'rgba(16, 185, 129, 0.8)'
                        ]
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            display: false
                        }
                    }
                }
            });
        }

        // Initialize file type chart
        const fileTypeCtx = document.getElementById('fileTypeChart');
        if (fileTypeCtx && typeof Chart !== 'undefined') {
            this.charts.fileType = new Chart(fileTypeCtx, {
                type: 'doughnut',
                data: {
                    labels: ['PDF', 'تصاویر', 'ویدیو', 'متن', 'سایر'],
                    datasets: [{
                        data: [0, 0, 0, 0, 0],
                        backgroundColor: [
                            '#ef4444',
                            '#f59e0b',
                            '#10b981',
                            '#3b82f6',
                            '#8b5cf6'
                        ]
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            position: 'bottom'
                        }
                    }
                }
            });
        }
    }

    setupSettingsToggles() {
        // Animation toggle
        const animationToggle = document.getElementById('enableAnimations');
        if (animationToggle) {
            animationToggle.checked = this.animationEnabled;
            animationToggle.addEventListener('change', (e) => {
                this.animationEnabled = e.target.checked;
                localStorage.setItem('dna-app-animations', this.animationEnabled);
            });
        }

        // Sound toggle
        const soundToggle = document.getElementById('enableSounds');
        if (soundToggle) {
            soundToggle.checked = this.soundEnabled;
            soundToggle.addEventListener('change', (e) => {
                this.soundEnabled = e.target.checked;
                localStorage.setItem('dna-app-sounds', this.soundEnabled);
            });
        }

        // Compression level slider
        const compressionSlider = document.getElementById('compressionLevel');
        const compressionValue = document.getElementById('compressionValue');
        if (compressionSlider && compressionValue) {
            compressionSlider.addEventListener('input', (e) => {
                compressionValue.textContent = e.target.value;
            });
        }

        // Default compression level slider
        const defaultCompressionSlider = document.getElementById('defaultCompressionLevel');
        const defaultCompressionValue = document.getElementById('defaultCompressionValue');
        if (defaultCompressionSlider && defaultCompressionValue) {
            defaultCompressionSlider.addEventListener('input', (e) => {
                defaultCompressionValue.textContent = e.target.value;
            });
        }
    }

    setupPasswordToggles() {
        // Encrypt password toggle
        const encryptPasswordToggle = document.getElementById('passwordToggle');
        const encryptPasswordInput = document.getElementById('encryptPassword');
        if (encryptPasswordToggle && encryptPasswordInput) {
            encryptPasswordToggle.addEventListener('click', () => {
                const type = encryptPasswordInput.type === 'password' ? 'text' : 'password';
                encryptPasswordInput.type = type;
                const icon = encryptPasswordToggle.querySelector('i');
                if (icon) {
                    icon.className = type === 'password' ? 'fas fa-eye' : 'fas fa-eye-slash';
                }
            });
        }

        // Decrypt password toggle
        const decryptPasswordToggle = document.getElementById('decryptPasswordToggle');
        const decryptPasswordInput = document.getElementById('decryptPassword');
        if (decryptPasswordToggle && decryptPasswordInput) {
            decryptPasswordToggle.addEventListener('click', () => {
                const type = decryptPasswordInput.type === 'password' ? 'text' : 'password';
                decryptPasswordInput.type = type;
                const icon = decryptPasswordToggle.querySelector('i');
                if (icon) {
                    icon.className = type === 'password' ? 'fas fa-eye' : 'fas fa-eye-slash';
                }
            });
        }
    }

    setupQuickActions() {
        // Quick encrypt button
        const quickEncryptBtns = document.querySelectorAll('[data-action="encrypt"]');
        quickEncryptBtns.forEach(btn => {
            btn.addEventListener('click', () => {
                this.switchTab('encrypt');
                const fileInput = document.getElementById('fileInput');
                if (fileInput) {
                    setTimeout(() => fileInput.click(), 100);
                }
            });
        });

        // Quick decrypt button
        const quickDecryptBtns = document.querySelectorAll('[data-action="decrypt"]');
        quickDecryptBtns.forEach(btn => {
            btn.addEventListener('click', () => {
                this.switchTab('decrypt');
                const dnaInput = document.getElementById('dnaInput');
                if (dnaInput) {
                    setTimeout(() => dnaInput.focus(), 100);
                }
            });
        });
    }

    setupFileHandling() {
        // File input click handler
        const fileDropZone = document.getElementById('fileDropZone');
        const fileInput = document.getElementById('fileInput');

        if (fileDropZone && fileInput) {
            fileDropZone.addEventListener('click', () => {
                fileInput.click();
            });
        }
    }

    setupKeyboardShortcuts() {
        document.addEventListener('keydown', (e) => {
            const key = [];
            if (e.ctrlKey) key.push('ctrl');
            if (e.altKey) key.push('alt');
            if (e.shiftKey) key.push('shift');
            key.push(e.key.toLowerCase());

            const shortcut = key.join('+');
            if (this.shortcuts[shortcut]) {
                e.preventDefault();
                this.shortcuts[shortcut]();
            }
        });
    }

    setupAnimations() {
        if (typeof gsap !== 'undefined' && this.animationEnabled) {
            // Animate dashboard cards on load
            gsap.from('.dashboard-card', {
                duration: 0.6,
                y: 30,
                opacity: 0,
                stagger: 0.1,
                ease: 'power2.out'
            });

            // Animate sidebar navigation
            gsap.from('.nav-item', {
                duration: 0.4,
                x: -20,
                opacity: 0,
                stagger: 0.05,
                delay: 0.2,
                ease: 'power2.out'
            });
        }
    }

    showProgress(text, progress = 0, step = 1) {
        // Create progress modal if it doesn't exist
        if (!this.progressModal) {
            this.createProgressModal();
        }

        if (this.progressModal) {
            this.progressModal.style.display = 'flex';
            this.updateProgress(progress, text, step);
        } else {
            // Fallback: show in console and notification
            console.log(`Progress: ${progress}% - ${text}`);
            this.showNotification(`${text} (${progress}%)`, 'info');
        }
    }

    createProgressModal() {
        // Create progress modal dynamically if it doesn't exist
        const modal = document.createElement('div');
        modal.id = 'progressModal';
        modal.className = 'progress-modal';
        modal.style.cssText = `
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.8);
            z-index: 10000;
            justify-content: center;
            align-items: center;
        `;

        modal.innerHTML = `
            <div class="progress-content" style="
                background: var(--bg-primary, #2a2a2a);
                border-radius: 12px;
                padding: 30px;
                text-align: center;
                color: var(--text-primary, #fff);
                min-width: 300px;
            ">
                <div class="progress-header">
                    <h3 id="progressTitle">در حال پردازش...</h3>
                </div>
                <div class="progress-body">
                    <div class="progress-circle" style="margin: 20px 0;">
                        <svg class="progress-ring" width="120" height="120">
                            <circle class="progress-ring-circle" cx="60" cy="60" r="54"
                                style="fill: none; stroke: #6366f1; stroke-width: 8; stroke-dasharray: 339.292; stroke-dashoffset: 339.292; transition: stroke-dashoffset 0.3s;"></circle>
                        </svg>
                        <div class="progress-percentage" id="progressPercentage" style="
                            position: absolute;
                            top: 50%;
                            left: 50%;
                            transform: translate(-50%, -50%);
                            font-size: 18px;
                            font-weight: bold;
                        ">0%</div>
                    </div>
                    <div class="progress-info">
                        <p id="progressText">آماده‌سازی...</p>
                    </div>
                </div>
            </div>
        `;

        document.body.appendChild(modal);

        // Update references
        this.progressModal = modal;
        this.progressRing = modal.querySelector('.progress-ring-circle');
        this.progressPercentage = modal.querySelector('#progressPercentage');
        this.progressText = modal.querySelector('#progressText');
    }

    updateProgress(progress, text = null, step = null) {
        if (this.progressPercentage) {
            this.progressPercentage.textContent = `${Math.round(progress)}%`;
        }

        if (this.progressRing) {
            const circumference = 2 * Math.PI * 54;
            const offset = circumference - (progress / 100) * circumference;
            this.progressRing.style.strokeDashoffset = offset;

            if (progress > 0) {
                this.progressRing.classList.add('active');
            }
        }

        if (text && this.progressText) {
            this.progressText.textContent = text;
        }

        if (step && this.progressSteps) {
            this.progressSteps.forEach((stepEl, index) => {
                stepEl.classList.remove('active', 'completed');
                if (index + 1 < step) {
                    stepEl.classList.add('completed');
                } else if (index + 1 === step) {
                    stepEl.classList.add('active');
                }
            });
        }
    }

    hideProgress() {
        if (this.progressModal) {
            this.progressModal.style.display = 'none';
        }

        // Reset progress ring
        if (this.progressRing) {
            this.progressRing.classList.remove('active');
            this.progressRing.style.strokeDashoffset = '339.292';
        }

        // Reset steps
        if (this.progressSteps) {
            this.progressSteps.forEach(step => {
                step.classList.remove('active', 'completed');
            });
        }
    }

    showNotification(message, type = 'info') {
        const notification = document.createElement('div');
        notification.className = `notification notification-${type}`;
        notification.innerHTML = `
            <span>${message}</span>
            <button class="notification-close" onclick="this.parentElement.remove()">×</button>
        `;
        
        this.notificationContainer.appendChild(notification);
        
        // حذف خودکار بعد از ۵ ثانیه
        setTimeout(() => {
            if (notification.parentElement) {
                notification.remove();
            }
        }, 5000);
    }

    showError(message) {
        this.showNotification(`❌ ${message}`, 'error');
    }

    showSuccess(message) {
        this.showNotification(`✅ ${message}`, 'success');
    }

    displayEncryptionResult(dnaSequence, encryptionId, danFilename) {
        const resultContainer = document.getElementById('encryptionResult');
        if (!resultContainer) return;

        const compressionRatio = this.calculateCompressionRatio(dnaSequence);
        
        resultContainer.innerHTML = `
            <div class="encryption-result">
                <h4>🧬 نتیجه رمزگذاری</h4>
                <div class="result-stats">
                    <p><strong>طول توالی DNA:</strong> ${dnaSequence.length.toLocaleString('fa-IR')} نوکلئوتید</p>
                    <p><strong>نرخ فشرده‌سازی:</strong> ${compressionRatio}%</p>
                    <p><strong>شناسه رمزگذاری:</strong> ${encryptionId}</p>
                </div>
                <div class="dna-sequence">${dnaSequence}</div>
                <div class="result-actions">
                    <button onclick="navigator.clipboard.writeText('${dnaSequence}')" class="btn btn-small">
                        📋 کپی توالی DNA
                    </button>
                    <button onclick="window.uiManager.downloadDNA('${dnaSequence}', '${danFilename || `dna_encrypted_${encryptionId}.dan`}')" class="btn btn-small">
                        💾 دانلود فایل DNA
                    </button>
                    <button onclick="window.uiManager.generateQRCode('${dnaSequence}')" class="btn btn-small">
                        📱 تولید کد QR
                    </button>
                </div>
            </div>
        `;
    }

    calculateCompressionRatio(dnaSequence) {
        // محاسبه ساده نرخ فشرده‌سازی
        return Math.round((dnaSequence.length / (dnaSequence.length * 2)) * 100);
    }

    downloadDNA(dnaSequence, filename) {
        const blob = new Blob([dnaSequence], { type: 'application/octet-stream' });
        const url = URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = filename || 'dna_encrypted.dan';
        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);
        URL.revokeObjectURL(url);
        
        // Show success notification
        this.showSuccess(`فایل ${filename || 'dna_encrypted.dan'} با موفقیت دانلود شد`);
    }

    offerDownload(data, filename) {
        const blob = new Blob([data]);
        const url = URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = filename;
        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);
        URL.revokeObjectURL(url);
        
        this.showSuccess(this.messages.decryptionComplete);
    }

    generateQRCode(dnaSequence) {
        // جای‌گذار برای تولید کد QR
        this.showNotification('قابلیت تولید کد QR به زودی اضافه خواهد شد!', 'info');
    }

    displayFileInfo(metadata) {
        const fileInfo = document.getElementById('fileInfo');
        if (!fileInfo) return;

        fileInfo.innerHTML = `
            <div class="file-info-card">
                <h4>📄 اطلاعات فایل</h4>
                <p><strong>نام:</strong> ${metadata.name}</p>
                <p><strong>اندازه:</strong> ${this.formatFileSize(metadata.size)}</p>
                <p><strong>نوع:</strong> ${metadata.type}</p>
                <p><strong>تاریخ تغییر:</strong> ${new Date(metadata.lastModified).toLocaleDateString('fa-IR')}</p>
            </div>
        `;
    }

    formatFileSize(bytes) {
        const sizes = ['بایت', 'کیلوبایت', 'مگابایت', 'گیگابایت'];
        if (bytes === 0) return '0 بایت';
        const i = Math.floor(Math.log(bytes) / Math.log(1024));
        const size = Math.round(bytes / Math.pow(1024, i) * 100) / 100;
        return `${size.toLocaleString('fa-IR')} ${sizes[i]}`;
    }

    // Navigation methods
    toggleSidebar() {
        if (this.sidebar) {
            this.sidebar.classList.toggle('show');
            // Remove the collapsed class toggle to ensure the menu can always be reopened
            // this.sidebar.classList.toggle('collapsed');
            
            // Instead, explicitly manage the collapsed state
            if (this.sidebar.classList.contains('show')) {
                this.sidebar.classList.remove('collapsed');
            } else {
                this.sidebar.classList.add('collapsed');
            }
            
            // Update the mobile menu toggle button state if it exists
            const mobileMenuToggle = document.getElementById('mobileMenuToggle');
            if (mobileMenuToggle) {
                const icon = mobileMenuToggle.querySelector('i');
                if (icon) {
                    if (this.sidebar.classList.contains('show')) {
                        icon.className = 'fas fa-times'; // X icon when menu is open
                    } else {
                        icon.className = 'fas fa-bars'; // Hamburger icon when menu is closed
                    }
                }
            }

            // Update sidebar open button visibility
            this.updateSidebarOpenBtnVisibility();
        }
    }

    switchTab(tabName) {
        // Prevent switching to the same tab
        if (this.currentTab === tabName) {
            return;
        }

        // Update navigation
        const navItems = document.querySelectorAll('.nav-item');
        navItems.forEach(item => {
            item.classList.remove('active');
            if (item.dataset.tab === tabName) {
                item.classList.add('active');
            }
        });

        // Update content with smooth transition
        const tabContents = document.querySelectorAll('.tab-content');
        const currentActiveTab = document.querySelector('.tab-content.active');
        const newActiveTab = document.querySelector(`#${tabName}-tab`);

        if (currentActiveTab && newActiveTab && currentActiveTab !== newActiveTab) {
            // Hide current tab
            currentActiveTab.classList.remove('active');

            // Show new tab after a brief delay
            setTimeout(() => {
                newActiveTab.classList.add('active');

                // Animate new tab in (only if animations are enabled)
                if (typeof gsap !== 'undefined' && this.animationEnabled) {
                    gsap.fromTo(newActiveTab,
                        { opacity: 0, y: 10 },
                        {
                            opacity: 1,
                            y: 0,
                            duration: 0.2,
                            ease: 'power2.out'
                        }
                    );
                }
            }, 50);
        } else if (newActiveTab) {
            // Direct switch if no current tab
            tabContents.forEach(content => content.classList.remove('active'));
            newActiveTab.classList.add('active');
        }

        this.currentTab = tabName;

        // Close sidebar on mobile after navigation
        if (window.innerWidth <= 768) {
            this.sidebar?.classList.remove('show');
            this.updateSidebarOpenBtnVisibility();
        }

        // Save current tab to localStorage
        localStorage.setItem('dna-app-current-tab', tabName);
    }

    // Utility methods
    toggleFullscreen() {
        if (!document.fullscreenElement) {
            document.documentElement.requestFullscreen();
        } else {
            document.exitFullscreen();
        }
    }

    updateSidebarOpenBtnVisibility() {
        const sidebarOpenBtn = document.getElementById('sidebarOpenBtn');
        if (!sidebarOpenBtn || !this.sidebar) return;

        if (this.sidebar.classList.contains('show')) {
            sidebarOpenBtn.style.display = 'none';
        } else {
            sidebarOpenBtn.style.display = 'flex';
        }
    }

    triggerFileSelect() {
        const fileInput = document.getElementById('fileInput');
        if (fileInput) {
            fileInput.click();
        }
    }

    closeModals() {
        const modals = document.querySelectorAll('.modal-overlay, .progress-modal');
        modals.forEach(modal => {
            modal.style.display = 'none';
        });
    }

    showShortcutsModal() {
        const modal = document.getElementById('shortcutsModal');
        if (modal) {
            modal.style.display = 'flex';
        }
    }

    toggleAdvancedSettings() {
        const settings = document.getElementById('advancedSettings');
        const toggle = document.getElementById('advancedToggle');

        if (settings && toggle) {
            settings.classList.toggle('collapsed');
            toggle.classList.toggle('collapsed');
        }
    }

    generateSecurePassword() {
        const length = 16;
        const charset = 'abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789!@#$%^&*';
        let password = '';

        for (let i = 0; i < length; i++) {
            password += charset.charAt(Math.floor(Math.random() * charset.length));
        }

        const passwordInput = document.getElementById('encryptPassword');
        if (passwordInput) {
            passwordInput.value = password;
            passwordInput.dispatchEvent(new Event('input'));
        }

        this.showNotification('رمز عبور امن تولید شد', 'success');
    }

    setupDNATools() {
        // DNA validation and tools
        const dnaInput = document.getElementById('dnaInput');
        if (dnaInput) {
            dnaInput.addEventListener('input', () => this.updateDNAStats());
        }

        // DNA tool buttons
        const pasteBtn = document.getElementById('pasteBtn');
        const clearBtn = document.getElementById('clearBtn');
        const validateBtn = document.getElementById('validateBtn');
        const formatBtn = document.getElementById('formatBtn');

        if (pasteBtn) {
            pasteBtn.addEventListener('click', () => this.pasteDNA());
        }

        if (clearBtn) {
            clearBtn.addEventListener('click', () => this.clearDNA());
        }

        if (validateBtn) {
            validateBtn.addEventListener('click', () => this.validateDNA());
        }

        if (formatBtn) {
            formatBtn.addEventListener('click', () => this.formatDNA());
        }
    }

    updateDNAStats() {
        const dnaInput = document.getElementById('dnaInput');
        if (!dnaInput) return;

        const sequence = dnaInput.value.toUpperCase().replace(/[^ATCG]/g, '');
        const stats = {
            length: sequence.length,
            A: (sequence.match(/A/g) || []).length,
            T: (sequence.match(/T/g) || []).length,
            C: (sequence.match(/C/g) || []).length,
            G: (sequence.match(/G/g) || []).length
        };

        // Update stats display
        const lengthEl = document.getElementById('dnaLength');
        const countA = document.getElementById('countA');
        const countT = document.getElementById('countT');
        const countC = document.getElementById('countC');
        const countG = document.getElementById('countG');

        if (lengthEl) lengthEl.textContent = stats.length.toLocaleString('fa-IR');
        if (countA) countA.textContent = stats.A.toLocaleString('fa-IR');
        if (countT) countT.textContent = stats.T.toLocaleString('fa-IR');
        if (countC) countC.textContent = stats.C.toLocaleString('fa-IR');
        if (countG) countG.textContent = stats.G.toLocaleString('fa-IR');
    }

    async pasteDNA() {
        try {
            const text = await navigator.clipboard.readText();
            const dnaInput = document.getElementById('dnaInput');
            if (dnaInput) {
                dnaInput.value = text;
                this.updateDNAStats();
                this.showNotification('متن از کلیپ‌بورد چسبانده شد', 'success');
            }
        } catch (err) {
            this.showNotification('خطا در دسترسی به کلیپ‌بورد', 'error');
        }
    }

    clearDNA() {
        const dnaInput = document.getElementById('dnaInput');
        if (dnaInput) {
            dnaInput.value = '';
            this.updateDNAStats();
            this.showNotification('متن پاک شد', 'info');
        }
    }

    validateDNA() {
        const dnaInput = document.getElementById('dnaInput');
        const validation = document.getElementById('dnaValidation');

        if (!dnaInput || !validation) return;

        const sequence = dnaInput.value.toUpperCase();
        const validChars = /^[ATCG\s\n\r]*$/;
        const isValid = validChars.test(sequence) && sequence.trim().length > 0;

        validation.className = `dna-validation-modern show ${isValid ? 'valid' : 'invalid'}`;
        validation.textContent = isValid ?
            '✅ توالی DNA معتبر است' :
            '❌ توالی DNA نامعتبر - فقط حروف A, T, C, G مجاز هستند';
    }

    formatDNA() {
        const dnaInput = document.getElementById('dnaInput');
        if (!dnaInput) return;

        let sequence = dnaInput.value.toUpperCase().replace(/[^ATCG]/g, '');

        // Format in groups of 10 with line breaks every 50
        let formatted = '';
        for (let i = 0; i < sequence.length; i += 50) {
            const line = sequence.substr(i, 50);
            const groups = line.match(/.{1,10}/g) || [];
            formatted += groups.join(' ') + '\n';
        }

        dnaInput.value = formatted.trim();
        this.updateDNAStats();
        this.showNotification('توالی DNA فرمت شد', 'success');
    }

    setupFileDropZone() {
        const dropZone = document.getElementById('fileDropZone');
        if (!dropZone) return;

        ['dragenter', 'dragover', 'dragleave', 'drop'].forEach(eventName => {
            dropZone.addEventListener(eventName, (e) => {
                e.preventDefault();
                e.stopPropagation();
            });
        });

        ['dragenter', 'dragover'].forEach(eventName => {
            dropZone.addEventListener(eventName, () => {
                dropZone.classList.add('dragover');
            });
        });

        ['dragleave', 'drop'].forEach(eventName => {
            dropZone.addEventListener(eventName, () => {
                dropZone.classList.remove('dragover');
            });
        });

        dropZone.addEventListener('drop', (e) => {
            const files = e.dataTransfer.files;
            if (files.length > 0) {
                const fileInput = document.getElementById('fileInput');
                if (fileInput) {
                    fileInput.files = files;
                    fileInput.dispatchEvent(new Event('change'));
                }
            }
        });
    }

    generateQRCode(dnaSequence) {
        if (typeof QRCode !== 'undefined') {
            // Create QR code modal
            const modal = document.createElement('div');
            modal.className = 'modal-overlay';
            modal.style.display = 'flex';
            modal.innerHTML = `
                <div class="modal-content">
                    <div class="modal-header">
                        <h3>کد QR توالی DNA</h3>
                        <button class="modal-close">&times;</button>
                    </div>
                    <div class="modal-body">
                        <div id="qrcode" style="text-align: center; padding: 20px;"></div>
                        <p style="text-align: center; margin-top: 10px;">اسکن کنید تا توالی DNA را دریافت کنید</p>
                    </div>
                </div>
            `;

            document.body.appendChild(modal);

            // Generate QR code
            QRCode.toCanvas(modal.querySelector('#qrcode'), dnaSequence, {
                width: 256,
                margin: 2
            });

            // Close modal handler
            modal.querySelector('.modal-close').addEventListener('click', () => {
                modal.remove();
            });

            modal.addEventListener('click', (e) => {
                if (e.target === modal) {
                    modal.remove();
                }
            });

            this.showNotification('کد QR تولید شد', 'success');
        } else {
            this.showNotification('کتابخانه QR Code بارگذاری نشده', 'error');
        }
    }

    handleResize() {
        // Handle responsive behavior
        if (window.innerWidth <= 768) {
            this.sidebar?.classList.add('collapsed');
        } else {
            this.sidebar?.classList.remove('collapsed');
        }

        // Resize charts
        Object.values(this.charts).forEach(chart => {
            if (chart && chart.resize) {
                chart.resize();
            }
        });
    }

    // Additional utility methods
    showConfirmDialog(message, onConfirm, onCancel) {
        const modal = document.createElement('div');
        modal.className = 'modal-overlay';
        modal.style.display = 'flex';
        modal.innerHTML = `
            <div class="modal-content">
                <div class="modal-header">
                    <h3>تأیید</h3>
                </div>
                <div class="modal-body">
                    <p>${message}</p>
                </div>
                <div class="modal-footer">
                    <button class="btn-modern btn-secondary" id="cancelBtn">لغو</button>
                    <button class="btn-modern btn-primary" id="confirmBtn">تأیید</button>
                </div>
            </div>
        `;

        document.body.appendChild(modal);

        modal.querySelector('#confirmBtn').addEventListener('click', () => {
            modal.remove();
            if (onConfirm) onConfirm();
        });

        modal.querySelector('#cancelBtn').addEventListener('click', () => {
            modal.remove();
            if (onCancel) onCancel();
        });

        modal.addEventListener('click', (e) => {
            if (e.target === modal) {
                modal.remove();
                if (onCancel) onCancel();
            }
        });
    }

    // Save settings button handler
    setupSettingsSaveButton() {
        const saveBtn = document.getElementById('settingsSaveBtn');
        if (saveBtn) {
            saveBtn.addEventListener('click', () => {
                this.saveSettings();
            });
        }
    }

    saveSettings() {
        // Save language
        const languageSelect = document.getElementById('language');
        if (languageSelect) {
            localStorage.setItem('dna-app-language', languageSelect.value);
            this.setLanguage(languageSelect.value);
        }

        // Save theme
        localStorage.setItem('dna-app-theme', this.currentTheme);

        // Save animations
        const animationToggle = document.getElementById('animationToggle');
        if (animationToggle) {
            localStorage.setItem('dna-app-animations', animationToggle.checked);
            this.animationEnabled = animationToggle.checked;
        }

        // Save sounds
        const soundToggle = document.getElementById('soundToggle');
        if (soundToggle) {
            localStorage.setItem('dna-app-sounds', soundToggle.checked);
            this.soundEnabled = soundToggle.checked;
        }

        // Save max file size
        const maxFileSizeInput = document.getElementById('maxFileSize');
        if (maxFileSizeInput) {
            localStorage.setItem('dna-app-max-file-size', maxFileSizeInput.value);
        }

        // Save compression level
        const compressionSlider = document.getElementById('compressionLevel');
        if (compressionSlider) {
            localStorage.setItem('dna-app-compression-level', compressionSlider.value);
        }

        // Save auto cleanup
        const autoCleanupSelect = document.getElementById('autoCleanup');
        if (autoCleanupSelect) {
            localStorage.setItem('dna-app-auto-cleanup', autoCleanupSelect.value);
        }

        // Save error correction
        const errorCorrectionToggle = document.getElementById('errorCorrectionToggle');
        if (errorCorrectionToggle) {
            localStorage.setItem('dna-app-error-correction', errorCorrectionToggle.checked);
        }

        this.showNotification('تنظیمات با موفقیت ذخیره شد', 'success');
    }

    playSound(type = 'success') {
        if (!this.soundEnabled) return;

        // Create audio context for sound effects
        try {
            const audioContext = new (window.AudioContext || window.webkitAudioContext)();
            const oscillator = audioContext.createOscillator();
            const gainNode = audioContext.createGain();

            oscillator.connect(gainNode);
            gainNode.connect(audioContext.destination);

            // Different frequencies for different types
            const frequencies = {
                success: 800,
                error: 300,
                info: 600,
                warning: 500
            };

            oscillator.frequency.setValueAtTime(frequencies[type] || 600, audioContext.currentTime);
            oscillator.type = 'sine';

            gainNode.gain.setValueAtTime(0.1, audioContext.currentTime);
            gainNode.gain.exponentialRampToValueAtTime(0.01, audioContext.currentTime + 0.3);

            oscillator.start(audioContext.currentTime);
            oscillator.stop(audioContext.currentTime + 0.3);
        } catch (error) {
            // Fallback for browsers that don't support Web Audio API
            console.log('Sound notification:', type);
        }
    }

    // Enhanced notification with sound
    showNotification(message, type = 'info', title = '') {
        const notification = document.createElement('div');
        notification.className = `notification notification-${type}`;

        const icons = {
            success: '✅',
            error: '❌',
            warning: '⚠️',
            info: 'ℹ️'
        };

        notification.innerHTML = `
            <div class="notification-icon">${icons[type] || icons.info}</div>
            <div class="notification-content">
                ${title ? `<div class="notification-title">${title}</div>` : ''}
                <div class="notification-message">${message}</div>
            </div>
            <button class="notification-close" onclick="this.parentElement.remove()">×</button>
        `;

        if (this.notificationContainer) {
            this.notificationContainer.appendChild(notification);
        } else {
            document.body.appendChild(notification);
        }

        // Play sound
        this.playSound(type);

        // Auto remove after 5 seconds
        setTimeout(() => {
            if (notification.parentElement) {
                notification.remove();
            }
        }, 5000);

        // Animate in
        if (typeof gsap !== 'undefined' && this.animationEnabled) {
            gsap.from(notification, {
                duration: 0.3,
                x: 300,
                opacity: 0,
                ease: 'power2.out'
            });
        }
    }

    // Switch to decryption tab for DNA encrypted files
    switchToDecryptionTab() {
        this.switchTab('decrypt');
        
        // Focus on password field
        setTimeout(() => {
            const passwordInput = document.getElementById('decryptPassword');
            if (passwordInput) {
                passwordInput.focus();
            }
        }, 300);
        
        // Show notice about DNA file detected
        this.showNotification('فایل رمزگذاری شده با DNA تشخیص داده شد. لطفاً رمز عبور را وارد کنید.', 'info', 'فایل DNA');
    }
}

// Ensure global availability
window.UIManager = UIManager;
