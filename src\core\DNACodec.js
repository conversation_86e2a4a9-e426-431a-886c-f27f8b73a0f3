class DNACodec {
    constructor() {
        this.nucleotideMap = {
            '00': 'A', '01': 'T', '10': 'C', '11': 'G'
        };
        this.reverseMap = {
            'A': '00', 'T': '01', 'C': '10', 'G': '11'
        };
        this.compressionLevel = 9; // Maximum compression level
        this.errorCorrectionEnabled = true;
        // Add additional compression options
        this.compressionStrategy = 2; // Z_BEST_COMPRESSION strategy
        this.compressionMemLevel = 9; // Maximum memory level for compression
    }

    // Advanced Encoding with Error Correction
    encodeWithErrorCorrection(data) {
        try {
            // Apply error correction if enabled
            const dataWithEC = this.errorCorrectionEnabled ? this.applyErrorCorrection(data) : data;
            
            // Compress the data
            const compressed = this.compress(dataWithEC);
            
            // Add checksum for integrity verification
            const withChecksum = this.addChecksum(compressed);
            
            // Add metadata including file information
            const withMetadata = this.embedMetadata(withChecksum);
            
            // Convert binary data to DNA sequence
            return this.binaryToDNA(withMetadata);
        } catch (error) {
            console.error('DNA encoding error:', error);
            throw new Error(`DNA encoding failed: ${error.message}`);
        }
    }

    // Decoding with Error Correction
    decodeWithErrorCorrection(dnaSequence) {
        try {
            // Clean the DNA sequence to ensure only valid nucleotides
            const cleanedSequence = this.cleanDNASequence(dnaSequence);
            
            // Convert DNA sequence to binary
            const binaryData = this.dnaToBinary(cleanedSequence);
            
            // Extract metadata
            const withoutMetadata = this.extractMetadata(binaryData);
            
            // Verify checksum
            const verified = this.verifyChecksum(withoutMetadata);
            
            // Decompress the data
            const decompressed = this.decompress(verified);
            
            // Apply error correction if enabled
            return this.errorCorrectionEnabled ? this.correctErrors(decompressed) : decompressed;
        } catch (error) {
            console.error('DNA decoding error:', error);
            throw new Error(`DNA decoding failed: ${error.message}`);
        }
    }

    // Clean DNA sequence to ensure only valid nucleotides
    cleanDNASequence(sequence) {
        // Remove any non-ATCG characters
        return sequence.toUpperCase().replace(/[^ATCG]/g, '');
    }

    // Apply error correction encoding
    applyErrorCorrection(data) {
        // Simple Reed-Solomon-like redundancy
        // For each byte, we add a parity byte
        const result = new Uint8Array(data.length * 2);
        
        for (let i = 0; i < data.length; i++) {
            result[i * 2] = data[i];
            result[i * 2 + 1] = this.calculateParity(data[i]);
        }
        
        return result;
    }
    
    // Correct errors using the redundancy data
    correctErrors(data) {
        // If the data length is odd, it's already corrupted
        if (data.length % 2 !== 0) {
            // Return as much as we can salvage
            return data.slice(0, Math.floor(data.length / 2));
        }
        
        const result = new Uint8Array(data.length / 2);
        let correctedErrors = 0;
        
        for (let i = 0; i < data.length / 2; i++) {
            const byte = data[i * 2];
            const parity = data[i * 2 + 1];
            
            if (this.calculateParity(byte) === parity) {
                // Data is correct
                result[i] = byte;
            } else {
                // Try to correct the error
                const corrected = this.attemptErrorCorrection(byte, parity);
                result[i] = corrected;
                correctedErrors++;
            }
        }
        
        console.log(`Corrected ${correctedErrors} errors during decoding`);
        return result;
    }
    
    // Calculate parity byte for error correction
    calculateParity(byte) {
        // XOR the byte with its bit-reversed version
        const reversed = this.reverseBits(byte);
        return byte ^ reversed;
    }
    
    // Attempt to correct an error in a byte using its parity
    attemptErrorCorrection(byte, parity) {
        // Try each bit position
        for (let bit = 0; bit < 8; bit++) {
            const flipped = byte ^ (1 << bit);
            if (this.calculateParity(flipped) === parity) {
                return flipped;
            }
        }
        // If we can't correct it, return the original
        return byte;
    }
    
    // Reverse the bits in a byte
    reverseBits(byte) {
        let result = 0;
        for (let i = 0; i < 8; i++) {
            result = (result << 1) | (byte & 1);
            byte >>= 1;
        }
        return result;
    }

    // Binary to DNA conversion
    binaryToDNA(data) {
        const binaryString = Array.from(data)
            .map(byte => byte.toString(2).padStart(8, '0'))
            .join('');
        
        let dnaSequence = '';
        for (let i = 0; i < binaryString.length; i += 2) {
            const pair = binaryString.substr(i, 2).padEnd(2, '0');
            dnaSequence += this.nucleotideMap[pair];
        }
        return dnaSequence;
    }

    // DNA to Binary conversion
    dnaToBinary(dnaSequence) {
        let binaryString = '';
        for (const nucleotide of dnaSequence) {
            if (!this.reverseMap[nucleotide]) {
                console.warn(`Invalid nucleotide found: ${nucleotide}, replacing with A`);
                binaryString += this.reverseMap['A'];
            } else {
                binaryString += this.reverseMap[nucleotide];
            }
        }
        
        const bytes = [];
        for (let i = 0; i < binaryString.length; i += 8) {
            const byte = binaryString.substr(i, 8);
            if (byte.length === 8) {
                bytes.push(parseInt(byte, 2));
            }
        }
        return new Uint8Array(bytes);
    }

    // Enhanced Compression using pako with maximum settings
    compress(data) {
        try {
            return pako.deflate(data, { 
                level: this.compressionLevel,
                strategy: this.compressionStrategy,
                memLevel: this.compressionMemLevel,
                windowBits: 15 // Maximum window bits
            });
        } catch (error) {
            console.error('Compression error:', error);
            // Return uncompressed data as fallback
            return data;
        }
    }

    decompress(data) {
        try {
            return pako.inflate(data);
        } catch (error) {
            console.error('Decompression error:', error);
            throw new Error('Failed to decompress data: possibly corrupted');
        }
    }

    // Add checksum for error detection
    addChecksum(data) {
        const checksum = this.calculateCRC32(data);
        const checksumBytes = new Uint8Array(4);
        new DataView(checksumBytes.buffer).setUint32(0, checksum);
        
        // Add a magic number before the checksum for better validation
        const magicNumber = new Uint8Array([0xDE, 0xAD, 0xBE, 0xEF]);
        
        return new Uint8Array([...data, ...magicNumber, ...checksumBytes]);
    }

    // Verify checksum
    verifyChecksum(data) {
        if (data.length < 8) throw new Error('Invalid data: too short for checksum and magic number');
        
        // Check for magic number
        const magicNumber = data.slice(-8, -4);
        const expectedMagic = new Uint8Array([0xDE, 0xAD, 0xBE, 0xEF]);
        
        const magicValid = magicNumber.every((byte, i) => byte === expectedMagic[i]);
        if (!magicValid) {
            throw new Error('Invalid data format: magic number not found');
        }
        
        const dataWithoutChecksum = data.slice(0, -8);
        const storedChecksum = new DataView(data.slice(-4).buffer).getUint32(0);
        const calculatedChecksum = this.calculateCRC32(dataWithoutChecksum);
        
        if (storedChecksum !== calculatedChecksum) {
            console.error(`Checksum mismatch: stored=${storedChecksum}, calculated=${calculatedChecksum}`);
            throw new Error('Checksum verification failed: data may be corrupted');
        }
        
        return dataWithoutChecksum;
    }

    // CRC32 calculation
    calculateCRC32(data) {
        const crcTable = this.generateCRC32Table();
        let crc = 0xFFFFFFFF;
        
        for (let i = 0; i < data.length; i++) {
            crc = (crc >>> 8) ^ crcTable[(crc ^ data[i]) & 0xFF];
        }
        
        return (crc ^ 0xFFFFFFFF) >>> 0;
    }

    generateCRC32Table() {
        const table = new Array(256);
        for (let i = 0; i < 256; i++) {
            let c = i;
            for (let j = 0; j < 8; j++) {
                c = (c & 1) ? (0xEDB88320 ^ (c >>> 1)) : (c >>> 1);
            }
            table[i] = c;
        }
        return table;
    }

    // Enhanced metadata embedding with original file extension
    embedMetadata(data) {
        const metadata = {
            timestamp: Date.now(),
            version: '2.1',
            originalSize: data.length,
            originalExtension: this.currentFileExtension || '', // Store original file extension
            errorCorrectionEnabled: this.errorCorrectionEnabled
        };
        
        const metadataString = JSON.stringify(metadata);
        const metadataBytes = new TextEncoder().encode(metadataString);
        
        // Add metadata length as a 4-byte header for easier extraction
        const metadataLength = new Uint8Array(4);
        new DataView(metadataLength.buffer).setUint32(0, metadataBytes.length);
        
        return new Uint8Array([...metadataLength, ...metadataBytes, ...data]);
    }

    // Enhanced metadata extraction with file extension retrieval
    extractMetadata(data) {
        if (data.length < 4) {
            throw new Error('Invalid data format: too short for metadata header');
        }
        
        // Extract metadata length from the first 4 bytes
        const metadataLength = new DataView(data.slice(0, 4).buffer).getUint32(0);
        
        if (data.length < 4 + metadataLength) {
            throw new Error('Invalid data format: metadata truncated');
        }
        
        const metadataBytes = data.slice(4, 4 + metadataLength);
        const actualData = data.slice(4 + metadataLength);
        
        try {
            const metadataString = new TextDecoder().decode(metadataBytes);
            const metadata = JSON.parse(metadataString);
            
            // Store the original extension for later use
            this.originalFileExtension = metadata.originalExtension || '';
            
            // Set error correction flag based on metadata
            if (metadata.errorCorrectionEnabled !== undefined) {
                this.errorCorrectionEnabled = metadata.errorCorrectionEnabled;
            }
            
            console.log('Extracted metadata:', metadata);
        } catch (error) {
            console.warn('Failed to parse metadata:', error);
            this.originalFileExtension = '';
        }
        
        return actualData;
    }

    // Store current file's extension for metadata
    setCurrentFileExtension(extension) {
        this.currentFileExtension = extension;
    }

    // Get original file extension after decoding
    getOriginalFileExtension() {
        return this.originalFileExtension || '';
    }

    // Set error correction mode
    setErrorCorrectionEnabled(enabled) {
        this.errorCorrectionEnabled = enabled;
    }

    // File type detection
    detectFileType(data) {
        const bytes = new Uint8Array(data);
        const signatures = {
            'image/jpeg': [0xFF, 0xD8, 0xFF],
            'image/png': [0x89, 0x50, 0x4E, 0x47],
            'application/pdf': [0x25, 0x50, 0x44, 0x46],
            'video/mp4': [0x00, 0x00, 0x00, 0x20, 0x66, 0x74, 0x79, 0x70],
            'application/zip': [0x50, 0x4B, 0x03, 0x04],
            'text/plain': [0x54, 0x68, 0x69, 0x73],
            'audio/mp3': [0x49, 0x44, 0x33]
        };

        for (const [mimeType, signature] of Object.entries(signatures)) {
            if (this.matchesSignature(bytes, signature)) {
                return mimeType;
            }
        }
        return 'application/octet-stream';
    }

    matchesSignature(bytes, signature) {
        return signature.every((byte, index) => bytes[index] === byte);
    }
}

// Ensure global availability
window.DNACodec = DNACodec;