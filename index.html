<!DOCTYPE html>
<html lang="fa" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🧬 پلتفرم رمزگذاری DNA - توسط Mezd</title>
    <meta name="description" content="پلتفرم پیشرفته رمزگذاری فایل‌ها با استفاده از کدگذاری DNA - توسعه‌یافته توسط Mezd">
    <meta name="author" content="Mezd">
    <link rel="stylesheet" href="styles/main.css">
    <style>
        :root {
            --primary-gradient: linear-gradient(135deg, #4f46e5, #9333ea);
            --secondary-gradient: linear-gradient(135deg, #0ea5e9, #3b82f6);
            --accent-gradient: linear-gradient(135deg, #f59e0b, #ef4444);
            --glass-bg: rgba(20, 20, 38, 0.8);
            --glass-border: rgba(255, 255, 255, 0.08);
            --card-shadow: 0 8px 32px rgba(0, 0, 0, 0.2);
            --animation-slow: 0.8s;
            --animation-normal: 0.5s;
            --animation-fast: 0.2s;
        }

        body {
            background-image: 
                radial-gradient(circle at 10% 20%, rgba(79, 70, 229, 0.1) 0%, transparent 30%),
                radial-gradient(circle at 90% 80%, rgba(147, 51, 234, 0.1) 0%, transparent 30%),
                radial-gradient(circle at 50% 50%, rgba(14, 165, 233, 0.05) 0%, transparent 80%);
            background-attachment: fixed;
        }

        /* Advanced DNA Animation Background */
        #dna-background {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            z-index: -1;
            opacity: 0.12;
            pointer-events: none;
        }

        /* Advanced Tab Transitions */
        .tab-content {
            opacity: 0;
            visibility: hidden;
            transition: opacity var(--animation-normal) cubic-bezier(0.4, 0, 0.2, 1), 
                        visibility var(--animation-normal) cubic-bezier(0.4, 0, 0.2, 1),
                        transform var(--animation-normal) cubic-bezier(0.4, 0, 0.2, 1);
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            transform: translateY(10px);
        }

        .tab-content.active {
            opacity: 1;
            visibility: visible;
            position: relative;
            transform: translateY(0);
        }

        .content-area {
            position: relative;
            min-height: 500px;
        }

        /* Modern Glass Morphism Cards */
        .encrypt-card, .decrypt-card, .dashboard-card {
            background: var(--glass-bg);
            backdrop-filter: blur(12px);
            -webkit-backdrop-filter: blur(12px);
            border: 1px solid var(--glass-border);
            box-shadow: var(--card-shadow);
        }
        
        /* Modern Navigation */
        .nav-item {
            transition: all var(--animation-normal) cubic-bezier(0.4, 0, 0.2, 1);
            position: relative;
            overflow: hidden;
        }

        .nav-item:before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 3px;
            height: 100%;
            background: var(--primary-gradient);
            transform: scaleY(0);
            transition: transform var(--animation-normal) cubic-bezier(0.4, 0, 0.2, 1);
            transform-origin: top;
        }

        .nav-item.active:before {
            transform: scaleY(1);
        }

        .nav-item.active {
            background: rgba(99, 102, 241, 0.15);
            font-weight: bold;
        }

        .nav-item:hover:not(.active) {
            background: rgba(255, 255, 255, 0.05);
        }

        .nav-link i {
            transition: transform var(--animation-fast) ease-out;
        }

        .nav-item:hover .nav-link i {
            transform: scale(1.2) translateX(-2px);
        }

        /* Modern Progress Modal */
        .progress-modal {
            backdrop-filter: blur(8px);
        }

        .progress-content {
            animation: modalScaleIn var(--animation-normal) cubic-bezier(0.34, 1.56, 0.64, 1);
            background: var(--glass-bg);
            border: 1px solid var(--glass-border);
            box-shadow: var(--card-shadow);
        }

        @keyframes modalScaleIn {
            from {
                opacity: 0;
                transform: scale(0.9) translateY(10px);
            }
            to {
                opacity: 1;
                transform: scale(1) translateY(0);
            }
        }

        /* Advanced DNA Loader Animation */
        .dna-loader {
            position: relative;
            width: 80px;
            height: 80px;
            margin: 0 auto;
            perspective: 1000px;
        }

        .dna-strand {
            position: absolute;
            width: 100%;
            height: 100%;
            border-radius: 50%;
            border-left: 2px solid #4f46e5;
            border-right: 2px solid #9333ea;
            animation: dna-rotate var(--animation-slow) linear infinite;
        }

        .dna-strand:nth-child(1) {
            animation-delay: -0.4s;
        }

        .dna-strand:nth-child(2) {
            animation-delay: -0.2s;
        }

        .dna-strand:nth-child(3) {
            animation-delay: 0s;
        }

        .dna-strand:nth-child(4) {
            width: 60%;
            height: 60%;
            top: 20%;
            left: 20%;
            border-left: 2px solid #0ea5e9;
            border-right: 2px solid #3b82f6;
        }

        @keyframes dna-rotate {
            0% {
                transform: rotateX(0deg) rotateY(0deg);
            }
            100% {
                transform: rotateX(360deg) rotateY(180deg);
            }
        }

        /* Advanced 3D Buttons */
        .btn-modern {
            position: relative;
            transition: all var(--animation-normal) cubic-bezier(0.4, 0, 0.2, 1);
            transform-style: preserve-3d;
            overflow: hidden;
        }

        .btn-modern:before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: linear-gradient(rgba(255, 255, 255, 0), rgba(255, 255, 255, 0.1));
            transform: translateY(-100%);
            transition: transform var(--animation-normal) cubic-bezier(0.4, 0, 0.2, 1);
        }

        .btn-modern:hover:before {
            transform: translateY(0);
        }

        .btn-modern:active {
            transform: translateY(2px) scale(0.98);
        }

        .btn-modern.btn-primary {
            background-image: var(--primary-gradient);
        }

        .btn-modern.btn-secondary {
            background-image: var(--secondary-gradient);
        }

        /* File Drop Zone Animation */
        .file-drop-zone {
            transition: all var(--animation-normal) cubic-bezier(0.4, 0, 0.2, 1);
            border: 2px dashed rgba(255, 255, 255, 0.15);
        }

        .file-drop-zone:hover, .file-drop-zone.highlight {
            border-color: #4f46e5;
            box-shadow: 0 0 0 2px rgba(79, 70, 229, 0.3);
            transform: translateY(-2px);
        }

        .upload-animation {
            transition: all var(--animation-normal) cubic-bezier(0.4, 0, 0.2, 1);
        }

        .file-drop-zone:hover .upload-animation {
            transform: scale(1.1);
        }

        .upload-ripple {
            width: 80px;
            height: 80px;
            border-radius: 50%;
            background: rgba(79, 70, 229, 0.1);
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            animation: ripple 2s ease-out infinite;
        }

        @keyframes ripple {
            0% {
                transform: translate(-50%, -50%) scale(0.5);
                opacity: 0.5;
            }
            100% {
                transform: translate(-50%, -50%) scale(2);
                opacity: 0;
            }
        }

        /* Modern Format Tags */
        .format-tag {
            transition: all var(--animation-normal) cubic-bezier(0.4, 0, 0.2, 1);
            transform: translateY(0);
        }

        .format-tag:hover {
            transform: translateY(-3px);
        }

        .format-tag.highlight {
            background: var(--primary-gradient);
            animation: pulse 2s infinite, float 3s ease-in-out infinite;
        }

        @keyframes float {
            0% { transform: translateY(0px); }
            50% { transform: translateY(-3px); }
            100% { transform: translateY(0px); }
        }

        /* Modern Notification System */
        .notification-container-modern {
            position: fixed;
            top: 20px;
            right: 20px;
            z-index: 1000;
            display: flex;
            flex-direction: column;
            gap: 10px;
        }

        .notification {
            animation: notificationSlideIn var(--animation-fast) cubic-bezier(0.68, -0.55, 0.27, 1.55);
        }
        
        /* Sidebar Open Button */
        .sidebar-open-btn {
            display: none;
            align-items: center;
            background: var(--glass-bg);
            border: 1px solid var(--glass-border);
            border-radius: 8px;
            color: white;
            cursor: pointer;
            font-size: 14px;
            padding: 8px 12px;
            margin-right: 10px;
            transition: all var(--animation-normal) cubic-bezier(0.4, 0, 0.2, 1);
        }
        
        .sidebar-open-btn i {
            margin-left: 6px;
        }
        
        .sidebar-open-btn:hover {
            background: rgba(99, 102, 241, 0.3);
            transform: translateY(-2px);
        }
        
        /* Show sidebar open button when sidebar is collapsed */
        .sidebar.collapsed ~ .main-content .sidebar-open-btn {
            display: flex;
        }
    </style>
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&display=swap" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.1/css/all.min.css" rel="stylesheet">
    <script src="https://cdnjs.cloudflare.com/ajax/libs/pako/2.1.0/pako.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/qrcode/1.5.3/qrcode.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/gsap@3.12.5/dist/gsap.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/three@0.158.0/build/three.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/lottie-web@5.12.2/build/player/lottie.min.js"></script>

    <!-- Load core modules -->
    <script src="src/core/DNACodec.js"></script>
    <script src="src/core/AdvancedCryptoEngine.js"></script>
    <script src="src/core/AdvancedFileHandler.js"></script>
    <script src="src/storage/StorageManager.js"></script>
    <script src="src/ui/UIManager.js"></script>
    <script src="src/ui/AdvancedFeatures.js"></script>
    <script src="src/DNAEncryptionApp.js"></script>
</head>
<body class="theme-dark" data-lang="fa">
    <!-- DNA Background Animation -->
    <div id="dna-background"></div>

    <!-- Loading Screen -->
    <div id="loadingScreen" class="loading-screen">
        <div class="loading-content">
            <div class="dna-loader">
                <div class="dna-strand"></div>
                <div class="dna-strand"></div>
                <div class="dna-strand"></div>
                <div class="dna-strand"></div>
            </div>
            <h2>🧬 DNA Encryption Platform</h2>
            <p>Loading advanced encryption system...</p>
            <div class="loading-progress">
                <div class="loading-bar"></div>
            </div>
        </div>
    </div>

    <div class="app-container">
        <!-- Sidebar Navigation -->
        <aside class="sidebar" id="sidebar">
            <div class="sidebar-header">
                <div class="logo">
                    <div class="logo-icon">🧬</div>
                    <span class="logo-text">DNA Crypto</span>
                </div>
                <button class="sidebar-toggle" id="sidebarToggle">
                    <i class="fas fa-bars"></i>
                </button>
            </div>

            <nav class="sidebar-nav">
                <ul class="nav-menu">
                    <li class="nav-item active" data-tab="dashboard">
                        <a href="#" class="nav-link">
                            <i class="fas fa-tachometer-alt"></i>
                            <span>داشبورد</span>
                        </a>
                    </li>
                    <li class="nav-item" data-tab="encrypt">
                        <a href="#" class="nav-link">
                            <i class="fas fa-lock"></i>
                            <span>رمزگذاری</span>
                        </a>
                    </li>
                    <li class="nav-item" data-tab="decrypt">
                        <a href="#" class="nav-link">
                            <i class="fas fa-unlock"></i>
                            <span>رمزگشایی</span>
                        </a>
                    </li>
                    <li class="nav-item" data-tab="analytics">
                        <a href="#" class="nav-link">
                            <i class="fas fa-chart-line"></i>
                            <span>آنالیز</span>
                        </a>
                    </li>
                    <li class="nav-item" data-tab="history">
                        <a href="#" class="nav-link">
                            <i class="fas fa-history"></i>
                            <span>تاریخچه</span>
                        </a>
                    </li>
                    <li class="nav-item" data-tab="settings">
                        <a href="#" class="nav-link">
                            <i class="fas fa-cog"></i>
                            <span>تنظیمات</span>
                        </a>
                    </li>
                </ul>
            </nav>

            <div class="sidebar-footer">
                <div class="user-profile">
                    <div class="user-avatar">M</div>
                    <div class="user-info">
                        <span class="user-name">Mezd</span>
                        <span class="user-role">Developer</span>
                    </div>
                </div>
                <button class="theme-toggle" id="themeToggle">
                    <i class="fas fa-moon"></i>
                </button>
            </div>
        </aside>

        <!-- Main Content Area -->
        <main class="main-content">
            <!-- Top Header -->
            <header class="top-header">
                <div class="header-left">
                    <button class="mobile-menu-toggle" id="mobileMenuToggle">
                        <i class="fas fa-bars"></i>
                    </button>
                    <!-- Add a sidebar open button that's visible when sidebar is closed -->
                    <button class="sidebar-open-btn" id="sidebarOpenBtn" style="display: none;">
                        <i class="fas fa-bars"></i>
                        <span>منو</span>
                    </button>
                    <h1 class="page-title">پلتفرم رمزگذاری DNA</h1>
                </div>

                <div class="header-right">
                    <div class="header-stats">
                        <div class="stat-card">
                            <div class="stat-icon">
                                <i class="fas fa-shield-alt"></i>
                            </div>
                            <div class="stat-info">
                                <span class="stat-number" id="totalEncryptions">0</span>
                                <span class="stat-label">رمزگذاری</span>
                            </div>
                        </div>
                        <div class="stat-card">
                            <div class="stat-icon">
                                <i class="fas fa-file"></i>
                            </div>
                            <div class="stat-info">
                                <span class="stat-number" id="totalFiles">0</span>
                                <span class="stat-label">فایل</span>
                            </div>
                        </div>
                        <div class="stat-card">
                            <div class="stat-icon">
                                <i class="fas fa-database"></i>
                            </div>
                            <div class="stat-info">
                                <span class="stat-number" id="totalBytes">0</span>
                                <span class="stat-label">حجم</span>
                            </div>
                        </div>
                    </div>
                    <div class="header-actions">
                        <button class="action-btn" id="notificationsBtn">
                            <i class="fas fa-bell"></i>
                            <span class="notification-badge">0</span>
                        </button>
                        <button class="action-btn" id="helpBtn">
                            <i class="fas fa-question-circle"></i>
                        </button>
                    </div>
                </div>
            </header>

            <!-- Main Content Section -->
            <div class="content-area">
                <!-- Dashboard Tab -->
                <section id="dashboard-tab" class="tab-content active">
                    <div class="dashboard-grid">
                        <!-- DNA 3D Animation Card -->
                        <div class="dashboard-card animation-card">
                            <div class="card-header">
                                <h3>شبیه‌سازی DNA</h3>
                                <div class="card-icon">
                                    <i class="fas fa-dna"></i>
                                </div>
                            </div>
                            <div class="card-content">
                                <div id="dnaAnimation" class="dna-animation-container"></div>
                            </div>
                        </div>
                        
                        <!-- Welcome Card -->
                        <div class="dashboard-card welcome-card">
                            <div class="card-header">
                                <h3>خوش آمدید به پلتفرم DNA</h3>
                                <div class="card-icon">
                                    <i class="fas fa-dna"></i>
                                </div>
                            </div>
                            <div class="card-content">
                                <p>سیستم پیشرفته رمزگذاری با استفاده از کدگذاری DNA</p>
                                <div class="quick-actions">
                                    <button class="quick-btn" data-action="encrypt">
                                        <i class="fas fa-lock"></i>
                                        رمزگذاری سریع
                                    </button>
                                    <button class="quick-btn" data-action="decrypt">
                                        <i class="fas fa-unlock"></i>
                                        رمزگشایی سریع
                                    </button>
                                </div>
                            </div>
                        </div>

                        <!-- Statistics Cards -->
                        <div class="dashboard-card stats-card">
                            <div class="card-header">
                                <h3>آمار عملکرد</h3>
                                <div class="card-icon">
                                    <i class="fas fa-chart-bar"></i>
                                </div>
                            </div>
                            <div class="card-content">
                                <div class="stats-grid">
                                    <div class="stat-item">
                                        <div class="stat-value">0</div>
                                        <div class="stat-label">رمزگذاری امروز</div>
                                    </div>
                                    <div class="stat-item">
                                        <div class="stat-value">0%</div>
                                        <div class="stat-label">نرخ موفقیت</div>
                                    </div>
                                    <div class="stat-item">
                                        <div class="stat-value">0ms</div>
                                        <div class="stat-label">میانگین زمان</div>
                                    </div>
                                    <div class="stat-item">
                                        <div class="stat-value">0MB</div>
                                        <div class="stat-label">حجم پردازش شده</div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Activity Chart -->
                        <div class="dashboard-card chart-card">
                            <div class="card-header">
                                <h3>نمودار فعالیت</h3>
                                <div class="chart-controls">
                                    <button class="chart-btn active" data-period="day">روز</button>
                                    <button class="chart-btn" data-period="week">هفته</button>
                                    <button class="chart-btn" data-period="month">ماه</button>
                                </div>
                            </div>
                            <div class="card-content">
                                <canvas id="activityChart"></canvas>
                            </div>
                        </div>

                        <!-- Recent Activity -->
                        <div class="dashboard-card activity-card">
                            <div class="card-header">
                                <h3>فعالیت‌های اخیر</h3>
                                <div class="card-icon">
                                    <i class="fas fa-clock"></i>
                                </div>
                            </div>
                            <div class="card-content">
                                <div class="activity-list" id="recentActivity">
                                    <div class="activity-item">
                                        <div class="activity-icon success">
                                            <i class="fas fa-check"></i>
                                        </div>
                                        <div class="activity-content">
                                            <div class="activity-title">سیستم آماده است</div>
                                            <div class="activity-time">همین الان</div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- System Status -->
                        <div class="dashboard-card status-card">
                            <div class="card-header">
                                <h3>وضعیت سیستم</h3>
                                <div class="status-indicator online"></div>
                            </div>
                            <div class="card-content">
                                <div class="status-list">
                                    <div class="status-item">
                                        <span class="status-label">موتور رمزگذاری</span>
                                        <span class="status-value online">آنلاین</span>
                                    </div>
                                    <div class="status-item">
                                        <span class="status-label">کدک DNA</span>
                                        <span class="status-value online">آماده</span>
                                    </div>
                                    <div class="status-item">
                                        <span class="status-label">ذخیره‌سازی</span>
                                        <span class="status-value online">متصل</span>
                                    </div>
                                </div>
                            </div>
                        </div>
            </div>
        </section>

        <!-- To-Do List Tab -->
        <section id="todo-tab" class="tab-content">
            <div class="todo-container">
                <div class="todo-header">
                    <h2>لیست کارها</h2>
                    <div class="todo-input-group">
                        <input type="text" id="todoInput" placeholder="کار جدید را وارد کنید...">
                        <button id="addTodoBtn" class="btn-modern btn-primary">افزودن</button>
                    </div>
                </div>
                <ul id="todoList" class="todo-list"></ul>
            </div>
        </section>

                <!-- Encryption Tab -->
                <section id="encrypt-tab" class="tab-content">
                    <div class="encrypt-container">
                        <!-- File Upload Section -->
                        <div class="encrypt-card file-upload-card">
                            <div class="card-header">
                                <h3>انتخاب فایل برای رمزگذاری</h3>
                                <div class="card-icon">
                                    <i class="fas fa-file-upload"></i>
                                </div>
                            </div>
                            <div class="card-content">
                                <div class="file-drop-zone" id="fileDropZone">
                                    <div class="drop-zone-content">
                                        <div class="upload-animation">
                                            <div class="upload-icon">
                                                <i class="fas fa-cloud-upload-alt"></i>
                                            </div>
                                            <div class="upload-ripple"></div>
                                        </div>
                                        <h4>فایل را اینجا بکشید یا کلیک کنید</h4>
                                        <p>حداکثر 500 مگابایت - تمام فرمت‌ها پشتیبانی می‌شوند</p>
                                        <div class="supported-formats">
                                            <span class="format-tag">PDF</span>
                                            <span class="format-tag">DOC</span>
                                            <span class="format-tag">IMG</span>
                                            <span class="format-tag">VIDEO</span>
                                            <span class="format-tag highlight">DNA</span>
                                            <span class="format-tag highlight">DAN</span>
                                            <span class="format-tag">+</span>
                                        </div>
                                    </div>
                                    <input type="file" id="fileInput" accept="*/*" hidden>
                                </div>
                                <div id="fileInfo" class="file-info-modern"></div>
                                <div class="file-preview" id="filePreview"></div>
                            </div>
                        </div>

                        <!-- Encryption Settings -->
                        <div class="encrypt-card settings-card">
                            <div class="card-header">
                                <h3>تنظیمات رمزگذاری</h3>
                                <div class="card-icon">
                                    <i class="fas fa-cogs"></i>
                                </div>
                            </div>
                            <div class="card-content">
                                <div class="input-group-modern">
                                    <label for="encryptPassword">رمز عبور امن</label>
                                    <div class="password-input-modern">
                                        <input type="password" id="encryptPassword" placeholder="رمز عبور قوی وارد کنید">
                                        <button type="button" class="password-toggle-modern" id="passwordToggle">
                                            <i class="fas fa-eye"></i>
                                        </button>
                                        <div class="password-generator">
                                            <button type="button" class="generate-btn" id="generatePassword">
                                                <i class="fas fa-random"></i>
                                            </button>
                                        </div>
                                    </div>
                                    <div id="passwordStrength" class="password-strength-modern">
                                        <div class="strength-bar">
                                            <div class="strength-fill"></div>
                                        </div>
                                        <span class="strength-text">قدرت رمز عبور</span>
                                    </div>
                                    <div class="password-requirements-modern">
                                        <div class="requirement" id="req-length">
                                            <i class="fas fa-times"></i>
                                            <span>حداقل 8 کاراکتر</span>
                                        </div>
                                        <div class="requirement" id="req-upper">
                                            <i class="fas fa-times"></i>
                                            <span>حروف بزرگ</span>
                                        </div>
                                        <div class="requirement" id="req-lower">
                                            <i class="fas fa-times"></i>
                                            <span>حروف کوچک</span>
                                        </div>
                                        <div class="requirement" id="req-number">
                                            <i class="fas fa-times"></i>
                                            <span>عدد</span>
                                        </div>
                                        <div class="requirement" id="req-special">
                                            <i class="fas fa-times"></i>
                                            <span>کاراکتر خاص</span>
                                        </div>
                                    </div>
                                </div>

                                <div class="advanced-settings">
                                    <div class="settings-header">
                                        <h4>تنظیمات پیشرفته</h4>
                                        <button class="collapse-btn" id="advancedToggle">
                                            <i class="fas fa-chevron-down"></i>
                                        </button>
                                    </div>
                                    <div class="settings-content" id="advancedSettings">
                                        <div class="setting-item">
                                            <div class="setting-info">
                                                <label>فشرده‌سازی</label>
                                                <span class="setting-desc">کاهش حجم فایل</span>
                                            </div>
                                            <div class="toggle-switch">
                                                <input type="checkbox" id="enableCompression" checked>
                                                <label for="enableCompression"></label>
                                            </div>
                                        </div>
                                        <div class="setting-item">
                                            <div class="setting-info">
                                                <label>تصحیح خطا</label>
                                                <span class="setting-desc">افزایش قابلیت اطمینان</span>
                                            </div>
                                            <div class="toggle-switch">
                                                <input type="checkbox" id="enableErrorCorrection" checked>
                                                <label for="enableErrorCorrection"></label>
                                            </div>
                                        </div>
                                        <div class="setting-item">
                                            <div class="setting-info">
                                                <label>سطح فشرده‌سازی</label>
                                                <span class="setting-desc">تعادل بین سرعت و حجم</span>
                                            </div>
                                            <div class="range-slider">
                                                <input type="range" id="compressionLevel" min="1" max="9" value="9">
                                                <div class="range-labels">
                                                    <span>سریع</span>
                                                    <span id="compressionValue">9</span>
                                                    <span>بهینه</span>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <button id="encryptBtn" class="btn-modern btn-primary">
                                    <div class="btn-content">
                                        <i class="fas fa-dna"></i>
                                        <span>رمزگذاری به DNA</span>
                                    </div>
                                    <div class="btn-loading">
                                        <div class="loading-spinner"></div>
                                    </div>
                                </button>
                            </div>
                        </div>
                    </div>

                    <!-- Results Section -->
                    <div id="encryptionResult" class="result-section-modern"></div>
                </section>

                <!-- Decryption Tab -->
                <section id="decrypt-tab" class="tab-content">
                    <div class="decrypt-container">
                        <div class="decrypt-card">
                            <div class="card-header">
                                <h3>رمزگشایی از DNA</h3>
                                <div class="card-icon">
                                    <i class="fas fa-unlock-alt"></i>
                                </div>
                            </div>
                            <div class="card-content">
                                <div class="input-group-modern">
                                    <label for="dnaInput">توالی DNA</label>
                                    <div class="dna-input-modern">
                                        <textarea id="dnaInput" placeholder="توالی DNA را اینجا وارد کنید (A, T, C, G)" rows="8"></textarea>
                                        <div class="dna-tools-modern">
                                            <button type="button" class="tool-btn-modern" id="pasteBtn">
                                                <i class="fas fa-paste"></i>
                                                <span>چسباندن</span>
                                            </button>
                                            <button type="button" class="tool-btn-modern" id="clearBtn">
                                                <i class="fas fa-trash"></i>
                                                <span>پاک کردن</span>
                                            </button>
                                            <button type="button" class="tool-btn-modern" id="validateBtn">
                                                <i class="fas fa-check-circle"></i>
                                                <span>اعتبارسنجی</span>
                                            </button>
                                            <button type="button" class="tool-btn-modern" id="formatBtn">
                                                <i class="fas fa-align-left"></i>
                                                <span>فرمت</span>
                                            </button>
                                        </div>
                                        <div class="dna-stats">
                                            <div class="stat">
                                                <span class="stat-label">طول:</span>
                                                <span class="stat-value" id="dnaLength">0</span>
                                            </div>
                                            <div class="stat">
                                                <span class="stat-label">A:</span>
                                                <span class="stat-value" id="countA">0</span>
                                            </div>
                                            <div class="stat">
                                                <span class="stat-label">T:</span>
                                                <span class="stat-value" id="countT">0</span>
                                            </div>
                                            <div class="stat">
                                                <span class="stat-label">C:</span>
                                                <span class="stat-value" id="countC">0</span>
                                            </div>
                                            <div class="stat">
                                                <span class="stat-label">G:</span>
                                                <span class="stat-value" id="countG">0</span>
                                            </div>
                                        </div>
                                    </div>
                                    <div id="dnaValidation" class="dna-validation-modern"></div>
                                </div>

                                <div class="input-group-modern">
                                    <label for="decryptPassword">رمز عبور</label>
                                    <div class="password-input-modern">
                                        <input type="password" id="decryptPassword" placeholder="رمز عبور را وارد کنید">
                                        <button type="button" class="password-toggle-modern" id="decryptPasswordToggle">
                                            <i class="fas fa-eye"></i>
                                        </button>
                                    </div>
                                </div>

                                <button id="decryptBtn" class="btn-modern btn-secondary">
                                    <div class="btn-content">
                                        <i class="fas fa-microscope"></i>
                                        <span>رمزگشایی از DNA</span>
                                    </div>
                                    <div class="btn-loading">
                                        <div class="loading-spinner"></div>
                                    </div>
                                </button>
                            </div>
                        </div>
                    </div>
                </section>

                <!-- Analytics Tab -->
                <section id="analytics-tab" class="tab-content">
                    <div class="analytics-container">
                        <div class="analytics-header">
                            <h2>آنالیز و گزارشات</h2>
                            <div class="analytics-controls">
                                <select class="time-range-select" id="timeRange">
                                    <option value="today">امروز</option>
                                    <option value="week">این هفته</option>
                                    <option value="month">این ماه</option>
                                    <option value="year">امسال</option>
                                </select>
                                <button class="export-btn" id="exportReport">
                                    <i class="fas fa-download"></i>
                                    خروجی گزارش
                                </button>
                            </div>
                        </div>

                        <div class="analytics-grid">
                            <div class="analytics-card">
                                <div class="card-header">
                                    <h3>نمودار عملکرد</h3>
                                </div>
                                <div class="card-content">
                                    <canvas id="performanceChart"></canvas>
                                </div>
                            </div>

                            <div class="analytics-card">
                                <div class="card-header">
                                    <h3>توزیع نوع فایل</h3>
                                </div>
                                <div class="card-content">
                                    <canvas id="fileTypeChart"></canvas>
                                </div>
                            </div>

                            <div class="analytics-card">
                                <div class="card-header">
                                    <h3>آمار تفصیلی</h3>
                                </div>
                                <div class="card-content">
                                    <div class="detailed-stats">
                                        <div class="stat-row">
                                            <span class="stat-label">میانگین زمان رمزگذاری:</span>
                                            <span class="stat-value">0ms</span>
                                        </div>
                                        <div class="stat-row">
                                            <span class="stat-label">نرخ فشرده‌سازی:</span>
                                            <span class="stat-value">0%</span>
                                        </div>
                                        <div class="stat-row">
                                            <span class="stat-label">بزرگترین فایل:</span>
                                            <span class="stat-value">0 MB</span>
                                        </div>
                                        <div class="stat-row">
                                            <span class="stat-label">کوچکترین فایل:</span>
                                            <span class="stat-value">0 MB</span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </section>

                <!-- History Tab -->
                <section id="history-tab" class="tab-content">
                    <div class="history-container-modern">
                        <div class="history-header">
                            <h2>تاریخچه رمزگذاری‌ها</h2>
                            <div class="history-controls">
                                <div class="search-box">
                                    <i class="fas fa-search"></i>
                                    <input type="text" placeholder="جستجو در تاریخچه..." id="historySearch">
                                </div>
                                <div class="filter-dropdown">
                                    <button class="filter-btn" id="filterBtn">
                                        <i class="fas fa-filter"></i>
                                        فیلتر
                                    </button>
                                    <div class="filter-menu" id="filterMenu">
                                        <label><input type="checkbox" value="success"> موفق</label>
                                        <label><input type="checkbox" value="failed"> ناموفق</label>
                                        <label><input type="checkbox" value="today"> امروز</label>
                                        <label><input type="checkbox" value="week"> این هفته</label>
                                    </div>
                                </div>
                                <button class="clear-history-btn" id="clearHistory">
                                    <i class="fas fa-trash"></i>
                                    پاک کردن همه
                                </button>
                            </div>
                        </div>

                        <div class="history-list" id="historyContainer">
                            <div class="no-history-modern" id="noHistoryMessage">
                                <div class="no-history-icon">
                                    <i class="fas fa-history"></i>
                                </div>
                                <h3>هنوز تاریخچه‌ای وجود ندارد</h3>
                                <p>پس از انجام اولین رمزگذاری، تاریخچه اینجا نمایش داده می‌شود</p>
                            </div>
                        </div>
                    </div>
                </section>

                <!-- Settings Tab -->
                <section id="settings-tab" class="tab-content">
                    <div class="settings-container-modern">
                        <div class="settings-header">
                            <h2>تنظیمات سیستم</h2>
                            <button class="reset-settings-btn" id="resetSettings">
                                <i class="fas fa-undo"></i>
                                بازگردانی پیش‌فرض
                            </button>
                        </div>

                        <div class="settings-grid">
                            <!-- General Settings -->
                            <div class="settings-card">
                                <div class="card-header">
                                    <h3>تنظیمات کلی</h3>
                                    <div class="card-icon">
                                        <i class="fas fa-cog"></i>
                                    </div>
                                </div>
                                <div class="card-content">
                                    <div class="setting-item-modern">
                                        <div class="setting-info">
                                            <label>زبان رابط کاربری</label>
                                            <span class="setting-desc">انتخاب زبان نمایش</span>
                                        </div>
                                        <select id="language" class="modern-select">
                                            <option value="fa">🇮🇷 فارسی</option>
                                            <option value="en">🇺🇸 انگلیسی</option>
                                            <option value="es">🇪🇸 اسپانیایی</option>
                                            <option value="de">🇩🇪 آلمانی</option>
                                            <option value="fr">🇫🇷 فرانسوی</option>
                                            <option value="zh">🇨🇳 چینی</option>
                                            <option value="ja">🇯🇵 ژاپنی</option>
                                            <option value="ru">🇷🇺 روسی</option>
                                        </select>
                                    </div>

                                    <div class="setting-item-modern">
                                        <div class="setting-info">
                                            <label>تم رنگی</label>
                                            <span class="setting-desc">حالت نمایش رابط</span>
                                        </div>
                                        <div class="theme-selector">
                                            <button class="theme-option" data-theme="light">
                                                <i class="fas fa-sun"></i>
                                                <span>روشن</span>
                                            </button>
                                            <button class="theme-option active" data-theme="dark">
                                                <i class="fas fa-moon"></i>
                                                <span>تاریک</span>
                                            </button>
                                            <button class="theme-option" data-theme="auto">
                                                <i class="fas fa-adjust"></i>
                                                <span>خودکار</span>
                                            </button>
                                        </div>
                            </div>

                            <div class="setting-item-modern">
                                <div class="setting-info">
                                    <label>انیمیشن‌ها</label>
                                    <span class="setting-desc">فعال‌سازی جلوه‌های بصری</span>
                                </div>
                                <div class="toggle-switch">
                                    <input type="checkbox" id="animationToggle" checked>
                                    <label for="animationToggle"></label>
                                </div>
                            </div>

                            <div class="setting-item-modern">
                                <div class="setting-info">
                                    <label>صدای اعلان</label>
                                    <span class="setting-desc">پخش صدا هنگام تکمیل عملیات</span>
                                </div>
                                <div class="toggle-switch">
                                    <input type="checkbox" id="soundToggle" checked>
                                    <label for="soundToggle"></label>
                                </div>
                            </div>

                            <!-- Save Button -->
                            <div class="setting-item-modern" style="grid-column: span 2; justify-content: center;">
                                <button id="settingsSaveBtn" class="btn-modern btn-primary" style="min-width: 150px;">
                                    ذخیره تنظیمات
                                </button>
                            </div>
                        </div>
                    </div>

                            <!-- Security Settings -->
                            <div class="settings-card">
                                <div class="card-header">
                                    <h3>تنظیمات امنیتی</h3>
                                    <div class="card-icon">
                                        <i class="fas fa-shield-alt"></i>
                                    </div>
                                </div>
                                <div class="card-content">
                                    <div class="setting-item-modern">
                                        <div class="setting-info">
                                            <label>حداکثر حجم فایل (MB)</label>
                                            <span class="setting-desc">محدودیت حجم فایل‌های قابل پردازش</span>
                                        </div>
                                        <div class="number-input-modern">
                                            <input type="number" id="maxFileSize" min="1" max="1024" value="500">
                                            <span class="input-unit">MB</span>
                                        </div>
                                    </div>

                                    <div class="setting-item-modern">
                                        <div class="setting-info">
                                            <label>سطح فشرده‌سازی پیش‌فرض</label>
                                            <span class="setting-desc">تعادل بین سرعت و حجم</span>
                                        </div>
                                        <div class="range-slider-modern">
                                            <input type="range" id="compressionLevel" min="0" max="9" value="9">
                                            <div class="range-labels">
                                                <span>سریع</span>
                                                <span id="compressionValue">9</span>
                                                <span>بهینه</span>
                                            </div>
                                        </div>
                                    </div>

                                    <div class="setting-item-modern">
                                        <div class="setting-info">
                                            <label>پاک‌سازی خودکار تاریخچه</label>
                                            <span class="setting-desc">حذف خودکار رکوردهای قدیمی</span>
                                        </div>
                                        <select id="autoCleanup" class="modern-select">
                                            <option value="never">هرگز</option>
                                            <option value="week">هفتگی</option>
                                            <option value="month">ماهانه</option>
                                            <option value="year">سالانه</option>
                                        </select>
                                    </div>
                                    
                                    <div class="setting-item-modern">
                                        <div class="setting-info">
                                            <label>تصحیح خطای DNA</label>
                                            <span class="setting-desc">افزودن کد تصحیح خطا به توالی DNA</span>
                                        </div>
                                        <div class="toggle-switch">
                                            <input type="checkbox" id="errorCorrectionToggle" checked>
                                            <label for="errorCorrectionToggle"></label>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- About Section -->
                            <div class="settings-card">
                                <div class="card-header">
                                    <h3>درباره سیستم</h3>
                                    <div class="card-icon">
                                        <i class="fas fa-info-circle"></i>
                                    </div>
                                </div>
                                <div class="card-content">
                                    <div class="about-content">
                                        <div class="app-logo">
                                            <div class="logo-icon-large">🧬</div>
                                            <h4>DNA Encryption Platform</h4>
                                            <p class="version">نسخه 2.0.0</p>
                                        </div>

                                        <div class="developer-info">
                                            <div class="developer-avatar">M</div>
                                            <div class="developer-details">
                                                <h5>توسعه‌دهنده: Mezd</h5>
                                                <p>متخصص امنیت سایبری و رمزنگاری</p>
                                            </div>
                                        </div>

                                        <div class="system-info">
                                            <div class="info-item">
                                                <span class="info-label">تاریخ ساخت:</span>
                                                <span class="info-value">2024</span>
                                            </div>
                                            <div class="info-item">
                                                <span class="info-label">نوع رمزگذاری:</span>
                                                <span class="info-value">AES-256 + DNA</span>
                                            </div>
                                            <div class="info-item">
                                                <span class="info-label">سطح امنیت:</span>
                                                <span class="info-value">نظامی</span>
                                            </div>
                                        </div>

                                        <div class="action-buttons">
                                            <button class="info-btn" id="checkUpdates">
                                                <i class="fas fa-sync"></i>
                                                بررسی به‌روزرسانی
                                            </button>
                                            <button class="info-btn" id="viewLicense">
                                                <i class="fas fa-file-contract"></i>
                                                مجوز استفاده
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </section>
            </div>
        </main>
    </div>

    <!-- Modern Progress Modal -->
    <div id="progressModal" class="progress-modal" style="display: none;">
        <div class="progress-content">
            <div class="progress-header">
                <h3 id="progressTitle">در حال پردازش...</h3>
                <button class="progress-close" id="progressClose">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="progress-body">
                <div class="progress-circle">
                    <svg class="progress-ring" width="120" height="120">
                        <circle class="progress-ring-circle" cx="60" cy="60" r="54"></circle>
                    </svg>
                    <div class="progress-percentage" id="progressPercentage">0%</div>
                </div>
                <div class="progress-info">
                    <p id="progressText">آماده‌سازی...</p>
                    <div class="progress-steps">
                        <div class="step active" data-step="1">
                            <i class="fas fa-file"></i>
                            <span>بارگذاری فایل</span>
                        </div>
                        <div class="step" data-step="2">
                            <i class="fas fa-lock"></i>
                            <span>رمزگذاری</span>
                        </div>
                        <div class="step" data-step="3">
                            <i class="fas fa-dna"></i>
                            <span>تبدیل به DNA</span>
                        </div>
                        <div class="step" data-step="4">
                            <i class="fas fa-check"></i>
                            <span>تکمیل</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Notification System -->
    <div id="notificationContainer" class="notification-container-modern"></div>

    <!-- Context Menu -->
    <div id="contextMenu" class="context-menu" style="display: none;">
        <ul class="context-menu-list">
            <li class="context-menu-item" data-action="copy">
                <i class="fas fa-copy"></i>
                <span>کپی</span>
            </li>
            <li class="context-menu-item" data-action="paste">
                <i class="fas fa-paste"></i>
                <span>چسباندن</span>
            </li>
            <li class="context-menu-item" data-action="select-all">
                <i class="fas fa-select-all"></i>
                <span>انتخاب همه</span>
            </li>
            <li class="context-menu-divider"></li>
            <li class="context-menu-item" data-action="download">
                <i class="fas fa-download"></i>
                <span>دانلود</span>
            </li>
        </ul>
    </div>

    <!-- Confirmation Modal -->
    <div id="confirmModal" class="modal-overlay" style="display: none;">
        <div class="modal-content">
            <div class="modal-header">
                <h3 id="confirmTitle">تأیید عملیات</h3>
                <button class="modal-close" id="confirmClose">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="modal-body">
                <p id="confirmMessage">آیا از انجام این عملیات اطمینان دارید؟</p>
            </div>
            <div class="modal-footer">
                <button class="btn-modern btn-secondary" id="confirmCancel">انصراف</button>
                <button class="btn-modern btn-danger" id="confirmOk">تأیید</button>
            </div>
        </div>
    </div>

    <!-- QR Code Modal -->
    <div id="qrModal" class="modal-overlay" style="display: none;">
        <div class="modal-content qr-modal-content">
            <div class="modal-header">
                <h3>کد QR توالی DNA</h3>
                <button class="modal-close" id="qrClose">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="modal-body">
                <div class="qr-container">
                    <div id="qrCode"></div>
                    <p>این کد QR حاوی توالی DNA رمزگذاری شده است</p>
                </div>
            </div>
            <div class="modal-footer">
                <button class="btn-modern btn-secondary" id="downloadQR">
                    <i class="fas fa-download"></i>
                    دانلود QR
                </button>
            </div>
        </div>
    </div>

    <!-- Keyboard Shortcuts Help -->
    <div id="shortcutsModal" class="modal-overlay" style="display: none;">
        <div class="modal-content shortcuts-modal">
            <div class="modal-header">
                <h3>میانبرهای صفحه‌کلید</h3>
                <button class="modal-close" id="shortcutsClose">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="modal-body">
                <div class="shortcuts-grid">
                    <div class="shortcut-item">
                        <kbd>Ctrl</kbd> + <kbd>O</kbd>
                        <span>انتخاب فایل</span>
                    </div>
                    <div class="shortcut-item">
                        <kbd>Ctrl</kbd> + <kbd>E</kbd>
                        <span>رمزگذاری</span>
                    </div>
                    <div class="shortcut-item">
                        <kbd>Ctrl</kbd> + <kbd>D</kbd>
                        <span>رمزگشایی</span>
                    </div>
                    <div class="shortcut-item">
                        <kbd>Ctrl</kbd> + <kbd>H</kbd>
                        <span>تاریخچه</span>
                    </div>
                    <div class="shortcut-item">
                        <kbd>Ctrl</kbd> + <kbd>,</kbd>
                        <span>تنظیمات</span>
                    </div>
                    <div class="shortcut-item">
                        <kbd>F11</kbd>
                        <span>تمام صفحه</span>
                    </div>
                    <div class="shortcut-item">
                        <kbd>Ctrl</kbd> + <kbd>?</kbd>
                        <span>راهنما</span>
                    </div>
                    <div class="shortcut-item">
                        <kbd>Esc</kbd>
                        <span>بستن مودال</span>
                    </div>
                </div>
            </div>
        </div>
    </div>
</body>
</html>


