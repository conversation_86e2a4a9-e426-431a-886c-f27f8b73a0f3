<!DOCTYPE html>
<html lang="fa" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🧬 DNA Encryption - Debug Mode</title>
    <style>
        :root {
            --primary-color: #4f46e5;
            --secondary-color: #0ea5e9;
            --success-color: #10b981;
            --warning-color: #f59e0b;
            --error-color: #ef4444;
            --background-dark: #0f172a;
            --card-bg-dark: #1e293b;
            --text-light: #f8fafc;
            --text-secondary: #94a3b8;
            --border-color: #334155;
            --animation-duration: 0.3s;
            --shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
        }
        
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Inter', <PERSON><PERSON>, sans-serif;
            margin: 0;
            padding: 0;
            background: var(--background-dark);
            color: var(--text-light);
            line-height: 1.6;
            overflow-x: hidden;
            min-height: 100vh;
        }
        
        .debug-header {
            background: linear-gradient(to right, #4f46e5, #6366f1);
            padding: 1.5rem;
            text-align: center;
            box-shadow: var(--shadow);
            position: relative;
            overflow: hidden;
        }
        
        .debug-header h1 {
            position: relative;
            z-index: 2;
            font-weight: 700;
            margin-bottom: 0.5rem;
        }
        
        .debug-header p {
            position: relative;
            z-index: 2;
            color: rgba(255, 255, 255, 0.8);
            max-width: 600px;
            margin: 0 auto;
        }
        
        .debug-header::before {
            content: '';
            position: absolute;
            top: -50%;
            left: -50%;
            width: 200%;
            height: 200%;
            background: linear-gradient(45deg, 
                rgba(79, 70, 229, 0) 0%, 
                rgba(79, 70, 229, 0.1) 100%);
            animation: rotate 20s linear infinite;
            z-index: 1;
        }
        
        @keyframes rotate {
            from { transform: rotate(0deg); }
            to { transform: rotate(360deg); }
        }
        
        .debug-container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 2rem;
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
            gap: 1.5rem;
        }
        
        .debug-section {
            background: var(--card-bg-dark);
            border-radius: 0.75rem;
            box-shadow: var(--shadow);
            overflow: hidden;
            transition: transform var(--animation-duration) ease, 
                       box-shadow var(--animation-duration) ease;
        }
        
        .debug-section:hover {
            transform: translateY(-5px);
            box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 
                        0 10px 10px -5px rgba(0, 0, 0, 0.04);
        }
        
        .debug-section-header {
            background: rgba(79, 70, 229, 0.1);
            padding: 1rem 1.5rem;
            border-bottom: 1px solid var(--border-color);
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .debug-section-header h2 {
            font-size: 1.25rem;
            font-weight: 600;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }
        
        .debug-section-content {
            padding: 1.5rem;
        }
        
        /* Navigation Test */
        .nav-test {
            display: flex;
            gap: 0.5rem;
            margin-bottom: 1.5rem;
            flex-wrap: wrap;
        }
        
        .nav-item {
            padding: 0.6rem 1.2rem;
            background: rgba(79, 70, 229, 0.2);
            border: 1px solid rgba(79, 70, 229, 0.3);
            color: white;
            cursor: pointer;
            border-radius: 0.5rem;
            transition: all var(--animation-duration) ease;
            font-weight: 500;
            position: relative;
            overflow: hidden;
        }
        
        .nav-item::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: linear-gradient(to bottom, rgba(255,255,255,0.1), rgba(255,255,255,0));
            transform: translateY(-100%);
            transition: transform var(--animation-duration) ease;
        }
        
        .nav-item:hover::before {
            transform: translateY(0);
        }
        
        .nav-item.active {
            background: var(--primary-color);
            box-shadow: 0 0 0 1px var(--primary-color), 
                       0 3px 6px rgba(79, 70, 229, 0.4);
        }
        
        .tab-content {
            display: none;
            padding: 1.5rem;
            background: rgba(30, 41, 59, 0.5);
            border-radius: 0.5rem;
            border: 1px solid var(--border-color);
            animation: fadeIn 0.5s ease;
        }
        
        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(10px); }
            to { opacity: 1; transform: translateY(0); }
        }
        
        .tab-content.active {
            display: block;
        }
        
        /* Buttons */
        .btn-row {
            display: flex;
            flex-wrap: wrap;
            gap: 0.75rem;
            margin-bottom: 1.5rem;
        }
        
        button {
            background: var(--primary-color);
            color: white;
            border: none;
            padding: 0.6rem 1.2rem;
            border-radius: 0.5rem;
            cursor: pointer;
            font-size: 0.9rem;
            font-weight: 500;
            transition: all var(--animation-duration) ease;
            display: flex;
            align-items: center;
            gap: 0.5rem;
            position: relative;
            overflow: hidden;
        }
        
        button::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: linear-gradient(to bottom, rgba(255,255,255,0.15), rgba(255,255,255,0));
            transform: translateY(-100%);
            transition: transform var(--animation-duration) ease;
        }
        
        button:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 
                       0 2px 4px -1px rgba(0, 0, 0, 0.06);
        }
        
        button:hover::before {
            transform: translateY(0);
        }
        
        button:active {
            transform: translateY(0);
        }
        
        button.success {
            background: var(--success-color);
        }
        
        button.warning {
            background: var(--warning-color);
        }
        
        button.error {
            background: var(--error-color);
        }
        
        /* Debug Log */
        .log-container {
            position: relative;
        }
        
        #log {
            background: #0f1729;
            color: #10b981;
            padding: 1rem;
            border-radius: 0.5rem;
            font-family: 'Fira Code', monospace;
            height: 300px;
            overflow-y: auto;
            white-space: pre-wrap;
            scrollbar-width: thin;
            scrollbar-color: var(--primary-color) #0f1729;
            border: 1px solid var(--border-color);
            line-height: 1.6;
        }
        
        .log-timestamp {
            color: var(--text-secondary);
        }
        
        .log-error {
            color: var(--error-color);
        }
        
        .log-success {
            color: var(--success-color);
        }
        
        .log-warning {
            color: var(--warning-color);
        }
        
        .log-controls {
            margin-top: 1rem;
            display: flex;
            justify-content: space-between;
        }
        
        .log-status {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            color: var(--text-secondary);
            font-size: 0.875rem;
        }
        
        .status-indicator {
            width: 10px;
            height: 10px;
            border-radius: 50%;
            background: var(--success-color);
            position: relative;
        }
        
        .status-indicator::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            border-radius: 50%;
            background: var(--success-color);
            animation: pulse 1.5s infinite;
        }
        
        @keyframes pulse {
            0% { transform: scale(1); opacity: 1; }
            100% { transform: scale(2.5); opacity: 0; }
        }
        
        /* Test Results */
        .test-result {
            display: flex;
            align-items: center;
            gap: 0.75rem;
            margin: 1rem 0;
            padding: 0.75rem 1rem;
            border-radius: 0.5rem;
            background: rgba(30, 41, 59, 0.5);
            border: 1px solid var(--border-color);
            transition: all var(--animation-duration) ease;
        }
        
        .test-result:hover {
            transform: translateX(5px);
        }
        
        .test-icon {
            width: 24px;
            height: 24px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        
        .test-success .test-icon {
            background: rgba(16, 185, 129, 0.2);
            color: var(--success-color);
        }
        
        .test-error .test-icon {
            background: rgba(239, 68, 68, 0.2);
            color: var(--error-color);
        }
        
        .test-warning .test-icon {
            background: rgba(245, 158, 11, 0.2);
            color: var(--warning-color);
        }
        
        .test-message {
            flex-grow: 1;
        }
        
        /* Animations */
        @keyframes slideInRight {
            from { opacity: 0; transform: translateX(50px); }
            to { opacity: 1; transform: translateX(0); }
        }
        
        .debug-section {
            animation: slideInRight var(--animation-duration) ease forwards;
            opacity: 0;
        }
        
        .debug-section:nth-child(1) { animation-delay: 0.1s; }
        .debug-section:nth-child(2) { animation-delay: 0.2s; }
        .debug-section:nth-child(3) { animation-delay: 0.3s; }
        .debug-section:nth-child(4) { animation-delay: 0.4s; }
        
        /* Responsive Design */
        @media (max-width: 768px) {
            .debug-container {
                grid-template-columns: 1fr;
            }
            
            .nav-test {
                flex-wrap: wrap;
            }
        }
    </style>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&family=Fira+Code:wght@400;500&display=swap" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.1/css/all.min.css" rel="stylesheet">
</head>
<body>
    <header class="debug-header">
        <h1>🧬 DNA Encryption Platform - Debug Mode</h1>
        <p>Advanced diagnostic tools and testing environment for the DNA Encryption Platform</p>
    </header>
    
    <div class="debug-container">
        <div class="debug-section">
            <div class="debug-section-header">
                <h2><i class="fas fa-bars"></i> Navigation Test</h2>
                <span id="navTestStatus" class="status-indicator"></span>
            </div>
            <div class="debug-section-content">
                <div class="nav-test">
                    <button class="nav-item active" data-tab="dashboard" onclick="switchTab('dashboard')">
                        <i class="fas fa-tachometer-alt"></i> داشبورد
                    </button>
                    <button class="nav-item" data-tab="encrypt" onclick="switchTab('encrypt')">
                        <i class="fas fa-lock"></i> رمزگذاری
                    </button>
                    <button class="nav-item" data-tab="decrypt" onclick="switchTab('decrypt')">
                        <i class="fas fa-unlock"></i> رمزگشایی
                    </button>
                    <button class="nav-item" data-tab="settings" onclick="switchTab('settings')">
                        <i class="fas fa-cog"></i> تنظیمات
                    </button>
                </div>
                
                <div id="dashboard-tab" class="tab-content active">
                    <h3><i class="fas fa-tachometer-alt"></i> داشبورد</h3>
                    <p>محتوای داشبورد اینجا نمایش داده می‌شود.</p>
                    <div id="dashboardTestResults"></div>
                </div>
                
                <div id="encrypt-tab" class="tab-content">
                    <h3><i class="fas fa-lock"></i> رمزگذاری</h3>
                    <p>محتوای رمزگذاری اینجا نمایش داده می‌شود.</p>
                    <div id="encryptTestResults"></div>
                </div>
                
                <div id="decrypt-tab" class="tab-content">
                    <h3><i class="fas fa-unlock"></i> رمزگشایی</h3>
                    <p>محتوای رمزگشایی اینجا نمایش داده می‌شود.</p>
                    <div id="decryptTestResults"></div>
                </div>
                
                <div id="settings-tab" class="tab-content">
                    <h3><i class="fas fa-cog"></i> تنظیمات</h3>
                    <p>محتوای تنظیمات اینجا نمایش داده می‌شود.</p>
                    <div id="settingsTestResults"></div>
                </div>
            </div>
        </div>

        <div class="debug-section">
            <div class="debug-section-header">
                <h2><i class="fas fa-vial"></i> UI Manager Test</h2>
                <span id="uiTestStatus" class="status-indicator"></span>
            </div>
            <div class="debug-section-content">
                <div class="btn-row">
                    <button onclick="testUIManager()">
                        <i class="fas fa-check-circle"></i> Test UI Manager
                    </button>
                    <button onclick="testProgressModal()">
                        <i class="fas fa-spinner"></i> Test Progress Modal
                    </button>
                </div>
                <div class="btn-row">
                    <button onclick="testNotifications()">
                        <i class="fas fa-bell"></i> Test Notifications
                    </button>
                    <button onclick="testThemeSwitch()">
                        <i class="fas fa-moon"></i> Test Theme Switch
                    </button>
                </div>
                <div id="uiTestResults"></div>
            </div>
        </div>
        
        <div class="debug-section">
            <div class="debug-section-header">
                <h2><i class="fas fa-dna"></i> DNA Codec Test</h2>
                <span id="codecTestStatus" class="status-indicator"></span>
            </div>
            <div class="debug-section-content">
                <div class="btn-row">
                    <button onclick="testDNACodec()">
                        <i class="fas fa-vial"></i> Test DNA Encoding
                    </button>
                    <button onclick="testErrorCorrection()">
                        <i class="fas fa-shield-alt"></i> Test Error Correction
                    </button>
                </div>
                <div class="btn-row">
                    <button onclick="testCompression()">
                        <i class="fas fa-compress-alt"></i> Test Compression
                    </button>
                    <button onclick="testFileExtension()">
                        <i class="fas fa-file-alt"></i> Test File Extension
                    </button>
                </div>
                <div id="codecTestResults"></div>
            </div>
        </div>

        <div class="debug-section">
            <div class="debug-section-header">
                <h2><i class="fas fa-terminal"></i> Debug Log</h2>
                <button onclick="clearLog()" class="warning">
                    <i class="fas fa-trash-alt"></i> Clear
                </button>
            </div>
            <div class="debug-section-content">
                <div class="log-container">
                    <div id="log"></div>
                    <div class="log-controls">
                        <div class="log-status">
                            <div class="status-indicator"></div>
                            <span>Console Active</span>
                        </div>
                        <div class="log-buttons">
                            <button class="success" onclick="testLog('success')">
                                <i class="fas fa-check-circle"></i> Success
                            </button>
                            <button class="warning" onclick="testLog('warning')">
                                <i class="fas fa-exclamation-triangle"></i> Warning
                            </button>
                            <button class="error" onclick="testLog('error')">
                                <i class="fas fa-times-circle"></i> Error
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Load the same scripts as main app -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/pako/2.1.0/pako.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/qrcode/1.5.3/qrcode.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/gsap@3.12.5/dist/gsap.min.js"></script>
    
    <!-- Load core modules -->
    <script src="src/core/DNACodec.js"></script>
    <script src="src/core/AdvancedCryptoEngine.js"></script>
    <script src="src/core/AdvancedFileHandler.js"></script>
    <script src="src/storage/StorageManager.js"></script>
    <script src="src/ui/UIManager.js"></script>
    <script src="src/ui/AdvancedFeatures.js"></script>
    <script src="src/DNAEncryptionApp.js"></script>

    <script>
        let currentTab = 'dashboard';
        let uiManager = null;

        function log(message, type = 'info') {
            const timestamp = new Date().toLocaleTimeString();
            const logElement = document.getElementById('log');
            const logEntry = document.createElement('div');
            
            // Create timestamp element
            const timestampSpan = document.createElement('span');
            timestampSpan.className = 'log-timestamp';
            timestampSpan.textContent = `[${timestamp}] `;
            
            // Create message element with appropriate class
            const messageSpan = document.createElement('span');
            messageSpan.className = type ? `log-${type}` : '';
            messageSpan.textContent = message;
            
            // Add elements to log entry
            logEntry.appendChild(timestampSpan);
            logEntry.appendChild(messageSpan);
            
            // Add entry to log
            logElement.appendChild(logEntry);
            logElement.scrollTop = logElement.scrollHeight;
            console.log(`[${type.toUpperCase()}] ${message}`);
        }

        function testLog(type) {
            const messages = {
                success: "This is a success test message!",
                warning: "This is a warning test message!",
                error: "This is an error test message!"
            };
            log(messages[type], type);
        }

        function clearLog() {
            const logElement = document.getElementById('log');
            logElement.innerHTML = '';
            log('Log cleared', 'info');
        }

        function switchTab(tabName) {
            log(`Switching to tab: ${tabName}`, 'info');
            
            // Update navigation
            document.querySelectorAll('.nav-item').forEach(item => {
                item.classList.remove('active');
                if (item.dataset.tab === tabName) {
                    item.classList.add('active');
                }
            });

            // Update content
            document.querySelectorAll('.tab-content').forEach(content => {
                content.classList.remove('active');
            });
            
            const newTab = document.getElementById(`${tabName}-tab`);
            if (newTab) {
                newTab.classList.add('active');
                log(`Successfully switched to ${tabName} tab`, 'success');
                
                // Add test result
                const resultContainer = document.getElementById(`${tabName}TestResults`);
                if (resultContainer) {
                    resultContainer.innerHTML = `
                        <div class="test-result test-success">
                            <div class="test-icon"><i class="fas fa-check"></i></div>
                            <div class="test-message">Tab switching successful: ${tabName}</div>
                        </div>
                    `;
                }
            } else {
                log(`ERROR: Tab ${tabName} not found`, 'error');
            }

            currentTab = tabName;
        }

        function displayTestResult(containerId, success, message, details = '') {
            const resultContainer = document.getElementById(containerId);
            if (!resultContainer) return;
            
            const resultClass = success ? 'test-success' : 'test-error';
            const icon = success ? 'check' : 'times';
            
            const resultHTML = `
                <div class="test-result ${resultClass}">
                    <div class="test-icon"><i class="fas fa-${icon}"></i></div>
                    <div class="test-message">
                        ${message}
                        ${details ? `<div class="test-details">${details}</div>` : ''}
                    </div>
                </div>
            `;
            
            resultContainer.innerHTML += resultHTML;
        }

        function testUIManager() {
            try {
                uiManager = new UIManager();
                log('UIManager created successfully', 'success');
                
                // Test basic functionality
                uiManager.currentTab = 'dashboard';
                log('UIManager basic properties set', 'success');
                
                displayTestResult('uiTestResults', true, 'UIManager initialized successfully');
                document.getElementById('uiTestStatus').style.background = 'var(--success-color)';
            } catch (error) {
                log(`ERROR: UIManager test failed - ${error.message}`, 'error');
                displayTestResult('uiTestResults', false, 'UIManager test failed', error.message);
                document.getElementById('uiTestStatus').style.background = 'var(--error-color)';
            }
        }

        function testProgressModal() {
            try {
                if (!uiManager) {
                    uiManager = new UIManager();
                }
                
                log('Testing progress modal...', 'info');
                uiManager.showProgress('Testing progress...', 25);
                log('Progress modal shown at 25%', 'success');
                
                setTimeout(() => {
                    uiManager.updateProgress(50, 'Half way there...');
                    log('Progress updated to 50%', 'success');
                    
                    setTimeout(() => {
                        uiManager.updateProgress(100, 'Complete!');
                        log('Progress updated to 100%', 'success');
                        
                        setTimeout(() => {
                            uiManager.hideProgress();
                            log('Progress modal hidden', 'success');
                            displayTestResult('uiTestResults', true, 'Progress modal test complete');
                        }, 1000);
                    }, 1000);
                }, 1000);
                
            } catch (error) {
                log(`ERROR: Progress modal test failed - ${error.message}`, 'error');
                displayTestResult('uiTestResults', false, 'Progress modal test failed', error.message);
            }
        }

        function testNotifications() {
            try {
                if (!uiManager) {
                    uiManager = new UIManager();
                }
                
                log('Testing notifications...', 'info');
                
                // Test success notification
                uiManager.showNotification('This is a success notification', 'success', 'Success Test');
                log('Success notification shown', 'success');
                
                // Test error notification after a delay
                setTimeout(() => {
                    uiManager.showNotification('This is an error notification', 'error', 'Error Test');
                    log('Error notification shown', 'success');
                    
                    // Test info notification after another delay
                    setTimeout(() => {
                        uiManager.showNotification('This is an info notification', 'info', 'Info Test');
                        log('Info notification shown', 'success');
                        displayTestResult('uiTestResults', true, 'Notification tests complete');
                    }, 1000);
                }, 1000);
                
            } catch (error) {
                log(`ERROR: Notification test failed - ${error.message}`, 'error');
                displayTestResult('uiTestResults', false, 'Notification test failed', error.message);
            }
        }

        function testThemeSwitch() {
            try {
                if (!uiManager) {
                    uiManager = new UIManager();
                }
                
                log('Testing theme switch...', 'info');
                
                // Get current theme
                const currentTheme = document.body.classList.contains('theme-light') ? 'light' : 'dark';
                log(`Current theme: ${currentTheme}`, 'info');
                
                // Switch to opposite theme
                const newTheme = currentTheme === 'light' ? 'dark' : 'light';
                uiManager.setTheme(newTheme);
                log(`Theme switched to ${newTheme}`, 'success');
                
                // Switch back after delay
                setTimeout(() => {
                    uiManager.setTheme(currentTheme);
                    log(`Theme switched back to ${currentTheme}`, 'success');
                    displayTestResult('uiTestResults', true, 'Theme switch test complete');
                }, 2000);
                
            } catch (error) {
                log(`ERROR: Theme switch test failed - ${error.message}`, 'error');
                displayTestResult('uiTestResults', false, 'Theme switch test failed', error.message);
            }
        }
        
        function testDNACodec() {
            try {
                const codec = new DNACodec();
                log('Created DNACodec instance', 'info');
                
                // Test simple encoding/decoding
                const testData = new TextEncoder().encode('Hello, DNA Encryption!');
                log(`Test data: "${new TextDecoder().decode(testData)}"`, 'info');
                
                // Encode data
                const encoded = codec.encodeWithErrorCorrection(testData);
                log(`Encoded data (first 50 chars): ${encoded.substring(0, 50)}...`, 'success');
                
                // Decode data
                const decoded = codec.decodeWithErrorCorrection(encoded);
                const decodedText = new TextDecoder().decode(decoded);
                log(`Decoded data: "${decodedText}"`, 'success');
                
                // Verify results
                const success = decodedText === 'Hello, DNA Encryption!';
                if (success) {
                    log('Encoding/decoding test passed!', 'success');
                    displayTestResult('codecTestResults', true, 'DNA encoding/decoding successful', `Length: ${encoded.length} nucleotides`);
                    document.getElementById('codecTestStatus').style.background = 'var(--success-color)';
                } else {
                    throw new Error('Decoded text does not match original');
                }
                
            } catch (error) {
                log(`ERROR: DNA codec test failed - ${error.message}`, 'error');
                displayTestResult('codecTestResults', false, 'DNA codec test failed', error.message);
                document.getElementById('codecTestStatus').style.background = 'var(--error-color)';
            }
        }

        function testErrorCorrection() {
            try {
                const codec = new DNACodec();
                log('Testing error correction...', 'info');
                
                // Create test data
                const testData = new TextEncoder().encode('Error correction test');
                
                // Encode with error correction
                const encoded = codec.encodeWithErrorCorrection(testData);
                log(`Original encoded data (first 30 chars): ${encoded.substring(0, 30)}...`, 'info');
                
                // Simulate errors by introducing mutations
                let mutatedEncoded = encoded.split('');
                // Mutate 3 random nucleotides
                for (let i = 0; i < 3; i++) {
                    const pos = Math.floor(Math.random() * encoded.length);
                    const nucleotides = ['A', 'T', 'G', 'C'].filter(n => n !== mutatedEncoded[pos]);
                    mutatedEncoded[pos] = nucleotides[Math.floor(Math.random() * nucleotides.length)];
                }
                mutatedEncoded = mutatedEncoded.join('');
                log(`Mutated encoded data (first 30 chars): ${mutatedEncoded.substring(0, 30)}...`, 'warning');
                
                // Try to decode with errors
                try {
                    const decoded = codec.decodeWithErrorCorrection(mutatedEncoded);
                    const decodedText = new TextDecoder().decode(decoded);
                    log(`Decoded after mutation: "${decodedText}"`, 'success');
                    
                    const success = decodedText === 'Error correction test';
                    if (success) {
                        log('Error correction test passed!', 'success');
                        displayTestResult('codecTestResults', true, 'Error correction successful', 'Recovered from 3 mutations');
                    } else {
                        log('Error correction partial recovery', 'warning');
                        displayTestResult('codecTestResults', false, 'Error correction partial recovery', `Expected: "Error correction test", Got: "${decodedText}"`);
                    }
                } catch (e) {
                    log(`Error correction failed: ${e.message}`, 'error');
                    displayTestResult('codecTestResults', false, 'Error correction failed', e.message);
                }
                
            } catch (error) {
                log(`ERROR: Error correction test failed - ${error.message}`, 'error');
                displayTestResult('codecTestResults', false, 'Error correction test failed', error.message);
            }
        }
        
        function testCompression() {
            try {
                const codec = new DNACodec();
                log('Testing compression...', 'info');
                
                // Create test data with repeating patterns for good compression
                const repeatingData = new TextEncoder().encode('ABCDEFGHIJKLMNOPQRSTUVWXYZ'.repeat(20));
                log(`Original data length: ${repeatingData.length} bytes`, 'info');
                
                // Encode with compression
                const encoded = codec.encodeWithErrorCorrection(repeatingData);
                log(`Encoded data length: ${encoded.length} nucleotides`, 'info');
                
                // Calculate compression ratio
                const originalBits = repeatingData.length * 8;
                const encodedBits = encoded.length * 2; // 2 bits per nucleotide
                const compressionRatio = (1 - encodedBits / originalBits) * 100;
                log(`Compression ratio: ${compressionRatio.toFixed(2)}%`, 'success');
                
                // Decode and verify
                const decoded = codec.decodeWithErrorCorrection(encoded);
                const success = decoded.length === repeatingData.length;
                
                for (let i = 0; i < repeatingData.length; i++) {
                    if (decoded[i] !== repeatingData[i]) {
                        throw new Error(`Mismatch at position ${i}: ${decoded[i]} vs ${repeatingData[i]}`);
                    }
                }
                
                if (success) {
                    log('Compression test passed!', 'success');
                    displayTestResult('codecTestResults', true, 'Compression successful', `Compression ratio: ${compressionRatio.toFixed(2)}%`);
                }
                
            } catch (error) {
                log(`ERROR: Compression test failed - ${error.message}`, 'error');
                displayTestResult('codecTestResults', false, 'Compression test failed', error.message);
            }
        }
        
        function testFileExtension() {
            try {
                const codec = new DNACodec();
                log('Testing file extension preservation...', 'info');
                
                // Set file extension
                const testExtension = '.pdf';
                codec.setCurrentFileExtension(testExtension);
                log(`Set original file extension: ${testExtension}`, 'info');
                
                // Create test data
                const testData = new TextEncoder().encode('PDF file content test');
                
                // Encode with metadata including extension
                const encoded = codec.encodeWithErrorCorrection(testData);
                log(`Encoded with extension metadata`, 'success');
                
                // Decode and check if extension was preserved
                const decoded = codec.decodeWithErrorCorrection(encoded);
                const retrievedExtension = codec.getOriginalFileExtension();
                log(`Retrieved file extension: ${retrievedExtension}`, 'info');
                
                if (retrievedExtension === testExtension) {
                    log('File extension preservation test passed!', 'success');
                    displayTestResult('codecTestResults', true, 'File extension preservation successful', `Extension "${testExtension}" correctly preserved`);
                } else {
                    throw new Error(`Extension mismatch: expected ${testExtension}, got ${retrievedExtension}`);
                }
                
            } catch (error) {
                log(`ERROR: File extension test failed - ${error.message}`, 'error');
                displayTestResult('codecTestResults', false, 'File extension test failed', error.message);
            }
        }
        
        // Initialize
        document.addEventListener('DOMContentLoaded', function() {
            log('Debug page loaded', 'info');
            log('Ready for testing', 'info');
        });
    </script>
</body>
</html>
