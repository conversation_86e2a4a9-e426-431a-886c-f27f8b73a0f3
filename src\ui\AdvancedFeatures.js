/**
 * DNA Encryption Platform - Advanced UI Features
 * Provides enhanced visual and interactive elements:
 * - 3D DNA Animation with Three.js
 * - GSAP-powered UI animations
 * - User preference management
 * - Sound effects system
 * 
 * <AUTHOR>
 * @version 1.0.0
 */

class AdvancedFeatures {
    constructor() {
        // Animation properties
        this.animationEnabled = true;
        this.soundEnabled = true;
        
        // 3D DNA Animation properties
        this.scene = null;
        this.camera = null;
        this.renderer = null;
        this.dnaModel = null;
        this.animationId = null;
        this.isAnimating = false;
        
        // DOM elements
        this.animationContainer = null;
        this.soundToggle = null;
        this.animationToggle = null;
        
        // User preferences
        this.preferences = {
            theme: 'dark',
            language: 'fa',
            animations: true,
            sounds: true,
            compression: 9, // 0-9 compression level
            errorCorrection: true
        };
        
        // Audio elements for sound effects
        this.audio = {
            success: null,
            error: null,
            notification: null,
            click: null,
            complete: null
        };
        
        // Initialize when page is loaded
        if (document.readyState === 'loading') {
            document.addEventListener('DOMContentLoaded', () => this.initialize());
        } else {
            this.initialize();
        }
    }

    /**
     * Initialize Advanced Features
     */
    async initialize() {
        try {
            // Load user preferences
            this.loadPreferences();
            
            // Setup UI elements
            this.setupUIElements();
            
            // Initialize sound effects
            this.initializeSounds();
            
            // Initialize animations if enabled
            if (this.preferences.animations) {
                this.initializeAnimations();
            }
            
            // Initialize 3D DNA animation if Three.js is available
            if (window.THREE && this.preferences.animations) {
                this.init3DDNA();
            }
            
            console.log('Advanced Features initialized successfully');
        } catch (error) {
            console.error('Failed to initialize Advanced Features:', error);
        }
    }
    
    /**
     * Load saved user preferences from localStorage
     */
    loadPreferences() {
        try {
            // Load theme
            const savedTheme = localStorage.getItem('dna-app-theme');
            if (savedTheme) {
                this.preferences.theme = savedTheme;
            }
            
            // Load language
            const savedLanguage = localStorage.getItem('dna-app-language');
            if (savedLanguage) {
                this.preferences.language = savedLanguage;
            }
            
            // Load animation setting
            const savedAnimations = localStorage.getItem('dna-app-animations');
            if (savedAnimations !== null) {
                this.preferences.animations = savedAnimations === 'true';
                this.animationEnabled = this.preferences.animations;
            }
            
            // Load sound setting
            const savedSounds = localStorage.getItem('dna-app-sounds');
            if (savedSounds !== null) {
                this.preferences.sounds = savedSounds === 'true';
                this.soundEnabled = this.preferences.sounds;
            }
            
            // Load compression level
            const savedCompression = localStorage.getItem('dna-app-compression');
            if (savedCompression !== null) {
                this.preferences.compression = parseInt(savedCompression, 10);
            }
            
            // Load error correction setting
            const savedErrorCorrection = localStorage.getItem('dna-app-error-correction');
            if (savedErrorCorrection !== null) {
                this.preferences.errorCorrection = savedErrorCorrection === 'true';
            }
        } catch (error) {
            console.error('Failed to load preferences:', error);
        }
    }
    
    /**
     * Save user preferences to localStorage
     */
    savePreferences() {
        try {
            localStorage.setItem('dna-app-theme', this.preferences.theme);
            localStorage.setItem('dna-app-language', this.preferences.language);
            localStorage.setItem('dna-app-animations', this.preferences.animations.toString());
            localStorage.setItem('dna-app-sounds', this.preferences.sounds.toString());
            localStorage.setItem('dna-app-compression', this.preferences.compression.toString());
            localStorage.setItem('dna-app-error-correction', this.preferences.errorCorrection.toString());
        } catch (error) {
            console.error('Failed to save preferences:', error);
        }
    }
    
    /**
     * Setup UI elements for advanced features
     */
    setupUIElements() {
        // Find animation container
        this.animationContainer = document.getElementById('dnaAnimation');
        
        // Setup settings toggles if they exist
        this.setupSettingsToggles();
    }
    
    /**
     * Setup settings page toggles
     */
    setupSettingsToggles() {
        // Animation toggle
        this.animationToggle = document.getElementById('animationToggle');
        if (this.animationToggle) {
            this.animationToggle.checked = this.preferences.animations;
            this.animationToggle.addEventListener('change', (e) => {
                this.preferences.animations = e.target.checked;
                this.animationEnabled = e.target.checked;
                this.savePreferences();
                
                if (this.animationEnabled) {
                    this.enableAnimations();
                } else {
                    this.disableAnimations();
                }
            });
        }
        
        // Sound toggle
        this.soundToggle = document.getElementById('soundToggle');
        if (this.soundToggle) {
            this.soundToggle.checked = this.preferences.sounds;
            this.soundToggle.addEventListener('change', (e) => {
                this.preferences.sounds = e.target.checked;
                this.soundEnabled = e.target.checked;
                this.savePreferences();
                
                // Play a test sound if enabling
                if (this.soundEnabled) {
                    this.playSound('notification');
                }
            });
        }
        
        // Compression level slider
        const compressionSlider = document.getElementById('compressionLevel');
        if (compressionSlider) {
            compressionSlider.value = this.preferences.compression;
            compressionSlider.addEventListener('input', (e) => {
                this.preferences.compression = parseInt(e.target.value, 10);
                document.getElementById('compressionValue').textContent = this.preferences.compression;
            });
            
            compressionSlider.addEventListener('change', () => {
                this.savePreferences();
                // Update DNACodec compression level if available
                if (window.DNACodec && window.app && window.app.dnaCodec) {
                    window.app.dnaCodec.compressionLevel = this.preferences.compression;
                }
            });
        }
        
        // Error correction toggle
        const errorCorrectionToggle = document.getElementById('errorCorrectionToggle');
        if (errorCorrectionToggle) {
            errorCorrectionToggle.checked = this.preferences.errorCorrection;
            errorCorrectionToggle.addEventListener('change', (e) => {
                this.preferences.errorCorrection = e.target.checked;
                this.savePreferences();
                
                // Update DNACodec error correction setting if available
                if (window.DNACodec && window.app && window.app.dnaCodec) {
                    window.app.dnaCodec.setErrorCorrectionEnabled(this.preferences.errorCorrection);
                    console.log(`Error correction ${this.preferences.errorCorrection ? 'enabled' : 'disabled'}`);
                    
                    // Show notification about the change
                    if (window.app && window.app.uiManager) {
                        window.app.uiManager.showNotification(
                            `تصحیح خطا ${this.preferences.errorCorrection ? 'فعال' : 'غیرفعال'} شد`,
                            this.preferences.errorCorrection ? 'success' : 'warning'
                        );
                    }
                }
            });
        }
    }
    
    /**
     * Initialize sound effects
     */
    initializeSounds() {
        // Create audio elements for sound effects
        const sounds = {
            success: 'data:audio/mp3;base64,SUQzBAAAAAABEVRYWFgAAAATAAADTGF2ZjU4Ljc2LjEwMAAAAAAAAAAAAAAA//tAwAAAAAAAAAAAAAAAAAAAAAAASW5mbwAAAA8AAAASAAAZfQANDQ0NDRoaGhoaGigoKCgoKDU1NTU1NUJCQkJCQk9PT09PT11dXV1dXWpqampqandDQ0NDQ0NDQ0NDQ0NDQ0NDQ0NDQ0NDQ0NDRUVFRUVFVVVVVVVVZGRER0Nzc3Nzc3NDREAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA//sQxAADwAABpAAAACAAADSAAAAETEFNRTMuMTAwVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVV//sQxBsAAAP8AAAAAMAAAIAAAAAAVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVV//sQxBsAAAP8AAAAAMAAAIAAAAAAVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVV//sQxBsAAAP8AAAAAMAAAIAAAAAAVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVV//tAxAAAAAAAAAAAAAAAAAAAAAAASW5mbwAAAA8AAAAKAAAkOwAwMDAwMDAwMDA/Pz8/Pz8/Pz8/Pz8/T09PT09PT09PX19fX19fX19fb29vb29vb29vj4+Pj4+Pj4+PAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA//sQxAADwAABpAAAACAAADSAAAAETEFNRTMuMTAwVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVX/+xDEFwAAAP8AAAAAMAAAIAAAAAAVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVX/+xDEFwAAAP8AAAAAMAAAIAAAAAAVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVX/+xDEFwAAAP8AAAAAMAAAIAAAAAAVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVX/',
            error: 'data:audio/mp3;base64,SUQzBAAAAAABEVRYWFgAAAATAAADTGF2ZjU4Ljc2LjEwMAAAAAAAAAAAAAAA//tAwAAAAAAAAAAAAAAAAAAAAAAASW5mbwAAAA8AAAALAAA7WgAQEBAQECAgICAgIDAwMDAwMEBAQEBAQFBQUFBQUGBgYGBgYHBwcHBwcICAgICAgJCQkJCQkKCgoKCgoAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA//sQxAADwAABpAAAACAAADSAAAAETEFNRTMuMTAwVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVV//sQxBsAAAP8AAAAAMAAAIAAAAAAVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVV//sQxBsAAAP8AAAAAMAAAIAAAAAAVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVV//sQxBsAAAP8AAAAAMAAAIAAAAAAVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVV',
            notification: 'data:audio/mp3;base64,SUQzBAAAAAABEVRYWFgAAAATAAADTGF2ZjU4Ljc2LjEwMAAAAAAAAAAAAAAA//tAwAAAAAAAAAAAAAAAAAAAAAAASW5mbwAAAA8AAAADAAAGQgBAQEBAQEBAQEBAmJiYmJiYmJiYmJiYmMDAwMDAwMDAwMDAwMAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAD//sQxAADwAABpAAAACAAADSAAAAETEFNRTMuMTAwVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVV//sQxBsAAAP8AAAAAMAAAIAAAAAAVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVV//sQxBsAAAP8AAAAAMAAAIAAAAAAVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVV//sQxBsAAAP8AAAAAMAAAIAAAAAAVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVV//tAwAAAAAAAAAAAAAAAAAAAAAAASW5mbwAAAA8AAAARAAAVtgAJCQwMDxISFRUYGBsdICAgIyYmKSksLC8vMjI1NTg4Ozs+PkFEREdHSkpNTU1QUFNTVFRZWV1dYGBiYmVlZ2dqam1tcHBzc3Z2eHh7e39/REREREREREAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==',
            click: 'data:audio/mp3;base64,SUQzBAAAAAABEVRYWFgAAAATAAADTGF2ZjU4Ljc2LjEwMAAAAAAAAAAAAAAA//tAwAAAAAAAAAAAAAAAAAAAAAAASW5mbwAAAA8AAAACAAAEWABAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQECAgICAgICAgICAgICAgICAgICAgICAgIAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAD/+xDEAAPAAAGkAAAAIAAANIAAAARMQU1FMy4xMDBVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVX/+xDEFwAAAP8AAAAAMAAAIAAAAAAVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVX/+xDEFwAAAP8AAAAAMAAAIAAAAAAVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVX/+xDEFwAAAP8AAAAAMAAAIAAAAAAVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVX/',
            complete: 'data:audio/mp3;base64,SUQzBAAAAAABEVRYWFgAAAATAAADTGF2ZjU4Ljc2LjEwMAAAAAAAAAAAAAAA//tAwAAAAAAAAAAAAAAAAAAAAAAASW5mbwAAAA8AAAAEAAAHAABcXFxcXFxcXFxcXHJycnJycnJycnJyiIiIiIiIiIiIiIienp6enp6enp6enp60tLS0tLS0tLS0tAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAD/+xDEAAPAAAGkAAAAIAAANIAAAARMQU1FMy4xMDBVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVX/+xDEFwAAAP8AAAAAMAAAIAAAAAAVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVX/+xDEFwAAAP8AAAAAMAAAIAAAAAAVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVX/+xDEFwAAAP8AAAAAMAAAIAAAAAAVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVX/'
        };
        
        // Create audio elements dynamically
        for (const [key, src] of Object.entries(sounds)) {
            this.audio[key] = new Audio(src);
            this.audio[key].volume = 0.5;
        }
    }
    
    /**
     * Play a sound if sound is enabled
     * @param {string} type - Type of sound to play (success, error, notification, click, complete)
     */
    playSound(type = 'notification') {
        if (this.soundEnabled && this.audio[type]) {
            this.audio[type].currentTime = 0;
            this.audio[type].play().catch(err => {
                console.warn('Sound play failed:', err);
            });
        }
    }
    
    /**
     * Initialize GSAP animations
     */
    initializeAnimations() {
        if (!window.gsap) return;
        
        // Apply animations to page elements
        this.animateUI();
    }
    
    /**
     * Apply GSAP animations to UI elements
     */
    animateUI() {
        if (!window.gsap) return;
        
        // Animate header elements
        gsap.fromTo('.app-header', 
            { y: -50, opacity: 0 }, 
            { y: 0, opacity: 1, duration: 0.8, ease: 'power2.out' }
        );
        
        // Animate sidebar items
        gsap.fromTo('.sidebar-item', 
            { x: -20, opacity: 0 }, 
            { x: 0, opacity: 1, duration: 0.5, stagger: 0.1, ease: 'back.out' }
        );
        
        // Animate cards
        gsap.fromTo('.card', 
            { y: 30, opacity: 0 }, 
            { y: 0, opacity: 1, duration: 0.7, stagger: 0.15, ease: 'power2.out' }
        );
        
        // Button hover effects
        document.querySelectorAll('.btn').forEach(btn => {
            btn.addEventListener('mouseenter', () => {
                if (!this.animationEnabled) return;
                
                gsap.to(btn, {
                    scale: 1.05,
                    duration: 0.2,
                    ease: 'power1.out'
                });
            });
            
            btn.addEventListener('mouseleave', () => {
                if (!this.animationEnabled) return;
                
                gsap.to(btn, {
                    scale: 1,
                    duration: 0.2,
                    ease: 'power1.out'
                });
            });
        });
        
        // Create hover animations for nav items
        document.querySelectorAll('.nav-item').forEach(item => {
            item.addEventListener('mouseenter', () => {
                if (!this.animationEnabled) return;
                
                gsap.to(item, {
                    backgroundColor: 'rgba(99, 102, 241, 0.3)',
                    duration: 0.3
                });
            });
            
            item.addEventListener('mouseleave', () => {
                if (!this.animationEnabled) return;
                
                if (!item.classList.contains('active')) {
                    gsap.to(item, {
                        backgroundColor: 'rgba(79, 70, 229, 0.2)',
                        duration: 0.3
                    });
                }
            });
        });
    }
    
    /**
     * Enable animations
     */
    enableAnimations() {
        this.animationEnabled = true;
        
        // Start 3D animation if container exists
        if (this.animationContainer && window.THREE) {
            this.start3DDNA();
        }
        
        // Re-apply UI animations
        this.animateUI();
    }
    
    /**
     * Disable animations
     */
    disableAnimations() {
        this.animationEnabled = false;
        
        // Stop 3D animation
        this.stop3DDNA();
        
        // Reset any ongoing GSAP animations
        if (window.gsap) {
            gsap.killTweensOf('*');
        }
    }
    
    /**
     * Initialize 3D DNA animation with Three.js
     */
    init3DDNA() {
        if (!window.THREE || !this.animationContainer) return;
        
        // Setup scene
        this.scene = new THREE.Scene();
        
        // Setup camera
        this.camera = new THREE.PerspectiveCamera(
            75, 
            this.animationContainer.clientWidth / this.animationContainer.clientHeight, 
            0.1, 
            1000
        );
        this.camera.position.z = 30;
        
        // Setup renderer
        this.renderer = new THREE.WebGLRenderer({ 
            antialias: true,
            alpha: true // Transparent background
        });
        this.renderer.setSize(this.animationContainer.clientWidth, this.animationContainer.clientHeight);
        this.renderer.setPixelRatio(window.devicePixelRatio);
        this.animationContainer.appendChild(this.renderer.domElement);
        
        // Create DNA structure
        this.createDNA();
        
        // Add ambient light
        const ambientLight = new THREE.AmbientLight(0x404040, 1);
        this.scene.add(ambientLight);
        
        // Add directional light
        const directionalLight = new THREE.DirectionalLight(0xffffff, 1);
        directionalLight.position.set(1, 1, 1);
        this.scene.add(directionalLight);
        
        // Handle window resize
        window.addEventListener('resize', () => this.onWindowResize());
        
        // Start animation if enabled
        if (this.animationEnabled) {
            this.start3DDNA();
        }
    }
    
    /**
     * Create DNA helix structure with Three.js
     */
    createDNA() {
        if (!window.THREE) return;
        
        // DNA parameters
        const nucleotides = 20; // Number of base pairs
        const helixRadius = 8;
        const helixHeight = 40;
        const nucleotideColors = [
            0x4f46e5, // A - Blue (Primary)
            0x0ea5e9, // T - Light Blue (Secondary)
            0x10b981, // C - Green (Success)
            0xef4444  // G - Red (Error)
        ];
        
        // Create helix group
        this.dnaModel = new THREE.Group();
        
        // Create helix strands
        for (let i = 0; i < 2; i++) {
            const curve = new THREE.CatmullRomCurve3([]);
            const points = 100;
            const offset = Math.PI * i; // Offset second strand by 180 degrees
            
            // Generate points for the helix curve
            for (let j = 0; j <= points; j++) {
                const t = j / points;
                const angle = t * Math.PI * 2 * nucleotides + offset;
                const x = helixRadius * Math.cos(angle);
                const y = helixHeight * (t - 0.5);
                const z = helixRadius * Math.sin(angle);
                
                curve.points.push(new THREE.Vector3(x, y, z));
            }
            
            // Create tube geometry for the strand
            const tubeGeometry = new THREE.TubeGeometry(
                curve,
                100,   // tubularSegments
                0.6,   // radius
                8,     // radialSegments
                false  // closed
            );
            
            // Create mesh for the strand
            const material = new THREE.MeshPhongMaterial({
                color: 0xffffff,
                shininess: 100,
                opacity: 0.9,
                transparent: true
            });
            
            const tube = new THREE.Mesh(tubeGeometry, material);
            this.dnaModel.add(tube);
        }
        
        // Add base pairs (nucleotides) connecting the two strands
        for (let i = 0; i < nucleotides; i++) {
            const t = i / (nucleotides - 1);
            const angle1 = t * Math.PI * 2 * nucleotides;
            const angle2 = angle1 + Math.PI;
            
            const x1 = helixRadius * Math.cos(angle1);
            const y1 = helixHeight * (t - 0.5);
            const z1 = helixRadius * Math.sin(angle1);
            
            const x2 = helixRadius * Math.cos(angle2);
            const y2 = y1;
            const z2 = helixRadius * Math.sin(angle2);
            
            const start = new THREE.Vector3(x1, y1, z1);
            const end = new THREE.Vector3(x2, y2, z2);
            
            // Create geometry for the base pair
            const baseGeometry = new THREE.CylinderGeometry(0.4, 0.4, start.distanceTo(end), 6);
            baseGeometry.rotateX(Math.PI / 2);
            
            // Random color from nucleotide colors
            const colorIndex = Math.floor(Math.random() * nucleotideColors.length);
            const baseMaterial = new THREE.MeshPhongMaterial({
                color: nucleotideColors[colorIndex],
                shininess: 80
            });
            
            const basePair = new THREE.Mesh(baseGeometry, baseMaterial);
            
            // Position and orient the base pair
            const midpoint = new THREE.Vector3().addVectors(start, end).multiplyScalar(0.5);
            basePair.position.copy(midpoint);
            
            // Look at target to orient cylinder
            basePair.lookAt(end);
            
            this.dnaModel.add(basePair);
        }
        
        // Add DNA to scene
        this.scene.add(this.dnaModel);
    }
    
    /**
     * Start 3D DNA animation
     */
    start3DDNA() {
        if (!this.renderer || !this.scene || !this.camera || this.isAnimating) return;
        
        this.isAnimating = true;
        
        const animate = () => {
            if (!this.isAnimating) return;
            
            this.animationId = requestAnimationFrame(animate);
            
            // Rotate DNA model
            if (this.dnaModel) {
                this.dnaModel.rotation.y += 0.005;
                this.dnaModel.rotation.z += 0.001;
            }
            
            // Render scene
            this.renderer.render(this.scene, this.camera);
        };
        
        animate();
    }
    
    /**
     * Stop 3D DNA animation
     */
    stop3DDNA() {
        this.isAnimating = false;
        
        if (this.animationId !== null) {
            cancelAnimationFrame(this.animationId);
            this.animationId = null;
        }
    }
    
    /**
     * Handle window resize for 3D animation
     */
    onWindowResize() {
        if (!this.camera || !this.renderer || !this.animationContainer) return;
        
        this.camera.aspect = this.animationContainer.clientWidth / this.animationContainer.clientHeight;
        this.camera.updateProjectionMatrix();
        this.renderer.setSize(this.animationContainer.clientWidth, this.animationContainer.clientHeight);
    }
}

// Initialize Advanced Features and make it available globally
window.AdvancedFeatures = AdvancedFeatures; 